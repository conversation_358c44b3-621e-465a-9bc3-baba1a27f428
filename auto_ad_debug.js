// ========================================
// 抖音去水印小程序广告自动刷脚本 - 调试版
// 用于分析界面内容和元素
// 版本：v3.4 (调试版)
// ========================================

console.log("调试脚本启动...");

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    toast(message);
    
    try {
        files.append("/sdcard/autojs_debug_log.txt", logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            return false;
        }
        log("权限检查通过");
        return true;
    } catch (e) {
        log("权限检查失败: " + e.message);
        return false;
    }
}

// 分析当前界面
function analyzeCurrentScreen() {
    log("=== 开始分析当前界面 ===");
    
    try {
        // 方法1: 获取所有文本节点
        log("方法1: 查找所有文本节点");
        try {
            var textNodes = text().find();
            log("找到文本节点数量: " + textNodes.length);
            
            for (var i = 0; i < Math.min(textNodes.length, 10); i++) {
                var node = textNodes[i];
                var nodeText = node.text();
                if (nodeText && nodeText.trim().length > 0) {
                    log("文本" + (i+1) + ": " + nodeText);
                }
            }
        } catch (e) {
            log("方法1失败: " + e.message);
        }
        
        // 方法2: 使用textContains查找
        log("方法2: 查找包含特定文本的节点");
        var keywords = ["抖音", "去水印", "广告", "金币", "观看", "视频"];
        
        for (var j = 0; j < keywords.length; j++) {
            try {
                var keyword = keywords[j];
                var nodes = textContains(keyword).find();
                if (nodes.length > 0) {
                    log("找到包含'" + keyword + "'的节点: " + nodes.length + "个");
                    for (var k = 0; k < Math.min(nodes.length, 3); k++) {
                        log("  - " + nodes[k].text());
                    }
                }
            } catch (e) {
                log("查找'" + keywords[j] + "'失败: " + e.message);
            }
        }
        
        // 方法3: 查找可点击元素
        log("方法3: 查找可点击元素");
        try {
            var clickableNodes = clickable(true).find();
            log("找到可点击元素数量: " + clickableNodes.length);
            
            for (var m = 0; m < Math.min(clickableNodes.length, 5); m++) {
                var clickNode = clickableNodes[m];
                var clickText = clickNode.text() || clickNode.desc() || "无文本";
                log("可点击元素" + (m+1) + ": " + clickText);
            }
        } catch (e) {
            log("方法3失败: " + e.message);
        }
        
        // 方法4: 获取根节点信息
        log("方法4: 分析根节点");
        try {
            var rootNode = className("android.widget.FrameLayout").findOne(1000);
            if (rootNode) {
                log("根节点类名: " + rootNode.className());
                log("根节点包名: " + rootNode.packageName());
            }
        } catch (e) {
            log("方法4失败: " + e.message);
        }
        
        // 方法5: 查找常见的小程序元素
        log("方法5: 查找小程序特征元素");
        var miniProgramKeywords = [
            "小程序",
            "微信",
            "WeChat",
            "返回",
            "更多",
            "分享"
        ];
        
        for (var n = 0; n < miniProgramKeywords.length; n++) {
            try {
                var mpKeyword = miniProgramKeywords[n];
                var mpNodes = textContains(mpKeyword).find();
                if (mpNodes.length > 0) {
                    log("找到小程序特征'" + mpKeyword + "': " + mpNodes.length + "个");
                }
            } catch (e) {
                log("查找小程序特征'" + miniProgramKeywords[n] + "'失败: " + e.message);
            }
        }
        
    } catch (e) {
        log("界面分析出错: " + e.message);
    }
    
    log("=== 界面分析完成 ===");
}

// 查找广告相关元素
function findAdElements() {
    log("=== 查找广告相关元素 ===");
    
    var adKeywords = [
        "广告",
        "观看",
        "视频",
        "奖励",
        "金币",
        "免费",
        "领取",
        "获得"
    ];
    
    for (var i = 0; i < adKeywords.length; i++) {
        try {
            var keyword = adKeywords[i];
            
            // 查找包含关键词的文本
            var textNodes = textContains(keyword).find();
            if (textNodes.length > 0) {
                log("找到包含'" + keyword + "'的文本: " + textNodes.length + "个");
                for (var j = 0; j < Math.min(textNodes.length, 3); j++) {
                    var node = textNodes[j];
                    log("  文本: " + node.text());
                    log("  可点击: " + node.clickable());
                    log("  类名: " + node.className());
                }
            }
            
            // 查找包含关键词的描述
            var descNodes = descContains(keyword).find();
            if (descNodes.length > 0) {
                log("找到包含'" + keyword + "'的描述: " + descNodes.length + "个");
            }
            
        } catch (e) {
            log("查找'" + adKeywords[i] + "'相关元素失败: " + e.message);
        }
    }
    
    log("=== 广告元素查找完成 ===");
}

// 主程序
function main() {
    log("=== 抖音去水印小程序界面调试脚本 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("屏幕分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 调试说明 ===");
        log("1. 请确保已在目标小程序界面");
        log("2. 脚本将分析当前界面的所有元素");
        log("3. 查看日志了解界面结构");
        
        // 延迟启动
        sleep(3000);
        
        // 分析当前界面
        analyzeCurrentScreen();
        
        // 查找广告相关元素
        findAdElements();
        
        log("调试完成，请查看日志文件");
        
    } catch (e) {
        log("调试脚本出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
}

// 启动脚本
console.log("准备启动调试脚本...");
main();
