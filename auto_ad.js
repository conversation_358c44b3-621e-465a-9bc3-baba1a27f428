// ========================================
// 微信小程序广告自动刷脚本 - 基于坐标点击
// 根据用户提供的完整流程优化
// 版本：v11.0 (完整流程版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.8, device.height * 0.5);
console.setPosition(100, 200);

console.log("🚀 微信小程序广告自动刷脚本启动...");


// ========================================
// 🎯 用户配置区域 - 根据需要修改这些参数
// ========================================

var USER_CONFIG = {
    // ⏰ 时间控制 - 可根据需要调整
    MAX_RUN_HOURS: 2,          // 最大运行时间（小时）
    INTERVAL_MIN_SEC: 15,      // 每轮最短间隔（秒）
    INTERVAL_MAX_SEC: 35,      // 每轮最长间隔（秒）
    AD_WAIT_MIN_SEC: 32,       // 广告观看最短时间（秒）
    AD_WAIT_MAX_SEC: 45,       // 广告观看最长时间（秒）
    LONG_BREAK_MIN_SEC: 180,   // 长休息最短时间（秒，3分钟）
    LONG_BREAK_MAX_SEC: 300,   // 长休息最长时间（秒，5分钟）

    // 🛡️ 防封号限制 - 重要安全参数
    MAX_CONTINUOUS_ADS: 15,    // 连续广告最大次数（超过后强制休息）
    DAILY_AD_LIMIT: 5000,       // 每日广告总数限制
    RANDOM_BREAK_CHANCE: 0.1,  // 随机休息概率（0.1 = 10%）
    MIN_ADS_BEFORE_BREAK: 5,   // 至少多少次广告后才可能随机休息

    // 🎯 坐标设置 - 根据您的设备调整
    WATCH_AD_X: 546,           // 观看广告按钮 X 坐标
    WATCH_AD_Y: 1152,          // 观看广告按钮 Y 坐标
    CLOSE_AD_X: 975,           // 关闭广告按钮 X 坐标
    CLOSE_AD_Y: 85,            // 关闭广告按钮 Y 坐标
    CONFIRM_X: 588,            // 确认按钮 X 坐标
    CONFIRM_Y: 1366,           // 确认按钮 Y 坐标

    // 🎲 人性化行为 - 模拟真实用户
    ENABLE_HUMAN_BEHAVIOR: true,  // 是否启用人性化行为
    CLICK_RANDOM_OFFSET: 15,      // 点击随机偏移范围（像素）
    CLICK_PRE_PAUSE_MIN: 200,     // 点击前最短停顿（毫秒）
    CLICK_PRE_PAUSE_MAX: 800,     // 点击前最长停顿（毫秒）
    CLICK_POST_PAUSE_MIN: 300,    // 点击后最短停顿（毫秒）
    CLICK_POST_PAUSE_MAX: 1000,   // 点击后最长停顿（毫秒）
    PRESS_DURATION_MIN: 50,       // 按压最短时间（毫秒）
    PRESS_DURATION_MAX: 150,      // 按压最长时间（毫秒）

    // 🔧 其他设置
    RETRY_TIMES: 3,            // 失败重试次数
    ENABLE_SAFE_MODE: true,    // 安全模式（检查应用状态）
    ENABLE_FILE_LOG: true,     // 是否保存日志到文件
    ENABLE_EMERGENCY_STOP: true // 紧急停止功能（音量键）
};

// ========================================
// 📋 配置说明
// ========================================
/*
🎯 参数调整建议：

⏰ 时间参数：
- MAX_RUN_HOURS: 建议6-8小时
- INTERVAL_MIN/MAX_SEC: 建议15-35秒，模拟真实间隔
- AD_WAIT_MIN/MAX_SEC: 建议32-45秒，确保广告完整播放
- LONG_BREAK_MIN/MAX_SEC: 建议3-5分钟休息

🛡️ 安全参数：
- MAX_CONTINUOUS_ADS: 建议10-20次
- DAILY_AD_LIMIT: 建议150-250次
- RANDOM_BREAK_CHANCE: 建议0.05-0.15（5%-15%）

🎯 坐标参数：
- 使用coordinate_finder.js工具获取准确坐标
- 根据设备分辨率调整

⚠️ 重要提醒：
1. 首次使用建议保守设置
2. 根据效果逐步调整
3. 定期更换参数避免固定模式
*/

// ========================================
// 内部配置 - 请勿修改
// ========================================

var CONFIG = {
    // 基础配置
    maxLoops: 999,
    intervalMin: USER_CONFIG.INTERVAL_MIN_SEC,
    intervalMax: USER_CONFIG.INTERVAL_MAX_SEC,
    adWaitTimeMin: USER_CONFIG.AD_WAIT_MIN_SEC,
    adWaitTimeMax: USER_CONFIG.AD_WAIT_MAX_SEC,
    maxRunTime: USER_CONFIG.MAX_RUN_HOURS * 60 * 60,

    // 防封号配置
    maxContinuousAds: USER_CONFIG.MAX_CONTINUOUS_ADS,
    longBreakMin: USER_CONFIG.LONG_BREAK_MIN_SEC,
    longBreakMax: USER_CONFIG.LONG_BREAK_MAX_SEC,
    dailyAdLimit: USER_CONFIG.DAILY_AD_LIMIT,
    randomBreakChance: USER_CONFIG.RANDOM_BREAK_CHANCE,
    minAdsBeforeRandomBreak: USER_CONFIG.MIN_ADS_BEFORE_BREAK,
    humanBehavior: USER_CONFIG.ENABLE_HUMAN_BEHAVIOR,

    // 坐标配置
    coordinates: {
        watchAd: { x: USER_CONFIG.WATCH_AD_X, y: USER_CONFIG.WATCH_AD_Y },
        closeAd: { x: USER_CONFIG.CLOSE_AD_X, y: USER_CONFIG.CLOSE_AD_Y },
        confirm: { x: USER_CONFIG.CONFIRM_X, y: USER_CONFIG.CONFIRM_Y }
    },

    // 人性化行为配置
    randomOffset: USER_CONFIG.CLICK_RANDOM_OFFSET,
    clickPrePauseMin: USER_CONFIG.CLICK_PRE_PAUSE_MIN,
    clickPrePauseMax: USER_CONFIG.CLICK_PRE_PAUSE_MAX,
    clickPostPauseMin: USER_CONFIG.CLICK_POST_PAUSE_MIN,
    clickPostPauseMax: USER_CONFIG.CLICK_POST_PAUSE_MAX,
    pressDurationMin: USER_CONFIG.PRESS_DURATION_MIN,
    pressDurationMax: USER_CONFIG.PRESS_DURATION_MAX,

    // 其他配置
    retryTimes: USER_CONFIG.RETRY_TIMES,
    safeMode: USER_CONFIG.ENABLE_SAFE_MODE,
    logToFile: USER_CONFIG.ENABLE_FILE_LOG,
    emergencyStopEnabled: USER_CONFIG.ENABLE_EMERGENCY_STOP
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;
var skipCount = 0;
var timeoutReached = false;
var continuousAdCount = 0;     // 连续广告计数
var lastBreakTime = 0;         // 上次休息时间
var todayAdCount = 0;          // 今日广告计数
var lastAdTime = 0;            // 上次广告时间

// 日志文件路径
var LOG_FILE = "/sdcard/wechat_miniprogram_ad_log.txt";

// 日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;

    // 控制台输出
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }

    // 文件日志
    if (CONFIG.logToFile) {
        try {
            files.append(LOG_FILE, logMsg + "\n");
        } catch (e) {
            // 忽略文件写入错误
        }
    }

    // Toast提示（重要信息）
    if (level === "SUCCESS" || level === "ERROR" || message.indexOf("完成") !== -1) {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");

    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            toast("请开启无障碍服务");
            return false;
        }
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 人性化点击函数（模拟真实用户行为）
function humanClick(x, y, description, useOffset) {
    useOffset = useOffset !== false; // 默认使用偏移

    try {
        var actualX = x;
        var actualY = y;

        // 添加随机偏移，模拟真实点击
        if (useOffset) {
            actualX += random(-CONFIG.randomOffset, CONFIG.randomOffset);
            actualY += random(-CONFIG.randomOffset, CONFIG.randomOffset);
        }

        // 确保坐标在屏幕范围内
        actualX = Math.max(0, Math.min(device.width, actualX));
        actualY = Math.max(0, Math.min(device.height, actualY));

        // 模拟人类点击前的短暂停顿
        if (CONFIG.humanBehavior) {
            var prePause = CONFIG.clickPrePauseMin + Math.random() * (CONFIG.clickPrePauseMax - CONFIG.clickPrePauseMin);
            sleep(prePause);
        }

        log("👆 点击: (" + actualX + ", " + actualY + ") - " + description);

        // 模拟按压时间
        if (CONFIG.humanBehavior) {
            var pressDuration = CONFIG.pressDurationMin + Math.random() * (CONFIG.pressDurationMax - CONFIG.pressDurationMin);
            press(actualX, actualY, pressDuration);
        } else {
            click(actualX, actualY);
        }

        // 点击后短暂停顿
        if (CONFIG.humanBehavior) {
            var postPause = CONFIG.clickPostPauseMin + Math.random() * (CONFIG.clickPostPauseMax - CONFIG.clickPostPauseMin);
            sleep(postPause);
        }

        log("✅ 点击成功: " + description, "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 生成随机广告观看时间
function getRandomAdWaitTime() {
    var minTime = CONFIG.adWaitTimeMin;
    var maxTime = CONFIG.adWaitTimeMax;
    var randomTime = minTime + Math.floor(Math.random() * (maxTime - minTime + 1));

    log("🎲 随机生成广告观看时间: " + randomTime + "秒 (范围: " + minTime + "-" + maxTime + "秒)");
    return randomTime;
}

// 生成随机间隔时间
function getRandomInterval() {
    var minTime = CONFIG.intervalMin;
    var maxTime = CONFIG.intervalMax;
    var randomTime = minTime + Math.floor(Math.random() * (maxTime - minTime + 1));

    log("🎲 随机生成间隔时间: " + randomTime + "秒 (范围: " + minTime + "-" + maxTime + "秒)");
    return randomTime;
}

// 生成随机长休息时间
function getRandomLongBreak() {
    var minTime = CONFIG.longBreakMin;
    var maxTime = CONFIG.longBreakMax;
    var randomTime = minTime + Math.floor(Math.random() * (maxTime - minTime + 1));

    log("😴 随机生成长休息时间: " + randomTime + "秒 (范围: " + minTime + "-" + maxTime + "秒)");
    return randomTime;
}

// 检查是否需要长休息
function shouldTakeLongBreak() {
    if (!CONFIG.humanBehavior) {
        return false;
    }

    // 连续广告次数达到限制
    if (continuousAdCount >= CONFIG.maxContinuousAds) {
        log("🛑 连续观看" + continuousAdCount + "次广告，需要长休息", "WARN");
        return true;
    }

    // 随机休息（模拟人类行为）
    if (continuousAdCount > CONFIG.minAdsBeforeRandomBreak && Math.random() < CONFIG.randomBreakChance) {
        log("🎲 随机触发长休息（模拟人类行为）", "WARN");
        return true;
    }

    return false;
}

// 检查每日限制
function checkDailyLimit() {
    if (todayAdCount >= CONFIG.dailyAdLimit) {
        log("⚠️ 已达到每日广告限制: " + CONFIG.dailyAdLimit + "次", "WARN");
        return false;
    }
    return true;
}

// 安全等待函数
function safeWait(seconds, description) {
    description = description || "等待";
    log("⏳ " + description + " " + seconds + " 秒...");

    try {
        sleep(seconds * 1000);
        return true;
    } catch (e) {
        log("❌ 等待被中断: " + e.message, "ERROR");
        return false;
    }
}

// 检查运行时间是否超限
function checkTimeLimit() {
    var currentTime = new Date().getTime();
    var runTime = Math.floor((currentTime - startTime) / 1000);
    var remainingTime = CONFIG.maxRunTime - runTime;

    if (runTime >= CONFIG.maxRunTime) {
        timeoutReached = true;
        log("⏰ 已达到8小时运行时间限制", "WARN");
        return false;
    }

    // 每30分钟显示一次剩余时间
    if (runTime % 1800 === 0 && runTime > 0) {
        var remainingHours = Math.floor(remainingTime / 3600);
        var remainingMinutes = Math.floor((remainingTime % 3600) / 60);
        log("⏰ 剩余运行时间: " + remainingHours + "小时" + remainingMinutes + "分钟");
    }

    return true;
}

// 获取格式化的运行时间
function getFormattedRunTime() {
    var currentTime = new Date().getTime();
    var runTime = Math.floor((currentTime - startTime) / 1000);
    var hours = Math.floor(runTime / 3600);
    var minutes = Math.floor((runTime % 3600) / 60);
    var seconds = runTime % 60;

    return hours + "小时" + minutes + "分" + seconds + "秒";
}

// 检查当前应用状态
function checkAppStatus() {
    if (!CONFIG.safeMode) {
        return true;
    }

    try {
        var currentPkg = currentActivity();
        log("📱 当前应用: " + currentPkg);

        // 检查是否在微信中
        if (currentPkg === "com.tencent.mm") {
            return "wechat";
        }
        // 检查是否在广告应用中
        else if (currentPkg !== "com.tencent.mm") {
            return "ad_app";
        }

        return "unknown";
    } catch (e) {
        log("⚠️ 无法检查应用状态: " + e.message, "WARN");
        return "unknown";
    }
}

// 执行完整的广告流程
function executeAdFlow() {
    log("🎬 开始执行广告流程...");

    try {
        // 检查每日限制
        if (!checkDailyLimit()) {
            log("❌ 已达到每日广告限制，停止执行", "ERROR");
            return false;
        }

        var coords = CONFIG.coordinates;

        // 步骤1: 点击"观看广告"按钮
        log("📍 步骤1: 点击观看广告按钮");
        if (!humanClick(coords.watchAd.x, coords.watchAd.y, "观看广告")) {
            log("❌ 点击观看广告失败", "ERROR");
            return false;
        }

        // 短暂等待广告加载
        safeWait(3, "等待广告加载");

        // 检查是否进入广告应用
        var appStatus = checkAppStatus();
        if (appStatus === "ad_app") {
            log("✅ 成功进入广告应用", "SUCCESS");
        } else {
            log("⚠️ 可能未进入广告应用，继续执行", "WARN");
        }

        // 步骤2: 等待广告播放完成（随机时间）
        log("📍 步骤2: 等待广告播放");
        var randomWaitTime = getRandomAdWaitTime();
        safeWait(randomWaitTime, "观看广告");

        // 步骤3: 点击关闭广告按钮
        log("📍 步骤3: 点击关闭广告按钮");
        if (!humanClick(coords.closeAd.x, coords.closeAd.y, "关闭广告")) {
            log("⚠️ 点击关闭广告失败，尝试返回键", "WARN");
            back();
            safeWait(2, "返回后等待");
        } else {
            safeWait(2, "关闭广告后等待");
        }

        // 步骤4: 点击确认按钮（太好了）
        log("📍 步骤4: 点击确认按钮");
        if (!humanClick(coords.confirm.x, coords.confirm.y, "确认按钮")) {
            log("⚠️ 点击确认按钮失败", "WARN");
        } else {
            safeWait(2, "确认后等待");
        }

        // 检查是否回到微信小程序
        appStatus = checkAppStatus();
        if (appStatus === "wechat") {
            log("✅ 成功回到微信小程序", "SUCCESS");
        }

        // 更新计数器
        adCount++;
        successCount++;
        continuousAdCount++;
        todayAdCount++;
        lastAdTime = new Date().getTime();

        log("🎉 第" + adCount + "次广告流程完成 (连续" + continuousAdCount + "次)", "SUCCESS");

        return true;

    } catch (e) {
        log("❌ 广告流程执行出错: " + e.message, "ERROR");
        errorCount++;
        return false;
    }
}

// 重试机制
function executeWithRetry() {
    for (var retry = 0; retry < CONFIG.retryTimes; retry++) {
        if (retry > 0) {
            log("🔄 第" + (retry + 1) + "次重试...");
            safeWait(3, "重试前等待");
        }

        if (executeAdFlow()) {
            return true;
        }

        log("❌ 第" + (retry + 1) + "次尝试失败");
    }

    log("❌ 所有重试都失败，跳过本轮", "ERROR");
    skipCount++;
    return false;
}

// 主循环
function mainLoop() {
    log("🚀 开始主循环", "SUCCESS");
    log("📋 配置信息:");
    log("  - 最大运行时间: 8小时");
    log("  - 每轮间隔: " + CONFIG.intervalMin + "-" + CONFIG.intervalMax + "秒 (随机)");
    log("  - 广告等待时间: " + CONFIG.adWaitTimeMin + "-" + CONFIG.adWaitTimeMax + "秒 (随机)");
    log("  - 随机偏移范围: ±" + CONFIG.randomOffset + "像素");
    log("  - 连续广告限制: " + CONFIG.maxContinuousAds + "次");
    log("  - 每日广告限制: " + CONFIG.dailyAdLimit + "次");
    log("  - 人性化行为: " + (CONFIG.humanBehavior ? "启用" : "禁用"));

    // 给用户准备时间
    log("⏰ 5秒后开始执行，请确保已在小程序界面...");
    safeWait(5, "准备时间");

    var loopCount = 0;

    while (true) {
        try {
            // 检查时间限制
            if (!checkTimeLimit()) {
                log("⏰ 达到8小时时间限制，停止执行", "WARN");
                break;
            }

            loopCount++;
            log("=== 📍 执行第 " + loopCount + " 轮 (运行时间: " + getFormattedRunTime() + ") ===");

            // 执行广告流程（带重试）
            var adSuccess = executeWithRetry();

            // 显示当前统计
            log("📊 当前统计: 成功" + successCount + "次, 失败" + errorCount + "次, 跳过" + skipCount + "次");
            log("📊 连续广告: " + continuousAdCount + "次, 今日总计: " + todayAdCount + "次");

            // 检查是否需要长休息
            if (adSuccess && shouldTakeLongBreak()) {
                var longBreakTime = getRandomLongBreak();
                log("😴 开始长休息 " + longBreakTime + "秒...", "WARN");
                safeWait(longBreakTime, "长休息");
                continuousAdCount = 0; // 重置连续计数
                lastBreakTime = new Date().getTime();
                log("😊 长休息结束，重新开始", "SUCCESS");
            }

            // 检查是否需要继续
            if (timeoutReached) {
                log("⏰ 时间限制已达到，准备结束", "WARN");
                break;
            }

            // 等待下一轮（随机间隔）
            var randomInterval = getRandomInterval();
            log("⏱️ 等待" + randomInterval + "秒后继续下一轮...");
            safeWait(randomInterval, "轮次间隔");

        } catch (e) {
            log("❌ 主循环出错: " + e.message, "ERROR");
            errorCount++;
            safeWait(5, "错误恢复等待");

            // 检查时间限制，即使出错也要遵守时间限制
            if (!checkTimeLimit()) {
                break;
            }
        }
    }

    if (timeoutReached) {
        log("🏁 主循环因时间限制结束 (8小时)", "SUCCESS");
    } else {
        log("🏁 主循环执行完成", "SUCCESS");
    }

    log("📊 总执行轮数: " + loopCount + "轮");
}

// 显示最终结果
function showFinalResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var hours = Math.floor(totalTime / 3600);
    var minutes = Math.floor((totalTime % 3600) / 60);
    var seconds = totalTime % 60;

    log("=== 📊 脚本执行完成 - 最终统计 ===", "SUCCESS");
    log("⏰ 总运行时间: " + hours + "小时" + minutes + "分" + seconds + "秒");

    if (timeoutReached) {
        log("⏰ 停止原因: 达到8小时时间限制", "WARN");
    } else {
        log("⏰ 停止原因: 手动停止或其他原因");
    }

    log("🎬 总尝试次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    log("⏭️ 跳过次数: " + skipCount + "次");

    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }

    // 计算效率
    if (totalTime > 0) {
        var adsPerHour = Math.round((successCount / totalTime) * 3600);
        var adsPerMinute = Math.round((successCount / totalTime) * 60);
        log("⚡ 效率: " + adsPerHour + "次/小时 (" + adsPerMinute + "次/分钟)");
    }

    // 计算时间利用率
    if (totalTime > 0) {
        var timeUtilization = Math.round((totalTime / CONFIG.maxRunTime) * 100);
        log("📊 时间利用率: " + timeUtilization + "% (运行了" + hours + "小时，最大8小时)");
    }

    log("📁 详细日志已保存到: " + LOG_FILE);

    // 最终提示
    var finalMessage = "脚本执行完成！运行" + hours + "小时" + minutes + "分，成功" + successCount + "次";
    toast(finalMessage);

    // 保持悬浮窗显示结果
    log("悬浮窗将保持显示15秒...");
    safeWait(15, "结果显示");
}

// 紧急停止功能
function setupEmergencyStop() {
    log("🛑 紧急停止: 音量下键连按3次可停止脚本");

    var volumeDownCount = 0;
    var lastVolumeDownTime = 0;

    events.observeKey();
    events.onKeyDown("volume_down", function () {
        var currentTime = new Date().getTime();

        if (currentTime - lastVolumeDownTime < 1000) {
            volumeDownCount++;
        } else {
            volumeDownCount = 1;
        }

        lastVolumeDownTime = currentTime;

        if (volumeDownCount >= 3) {
            log("🛑 检测到紧急停止信号，脚本即将停止", "WARN");
            toast("紧急停止！脚本即将退出");
            showFinalResults();
            exit();
        }
    });
}

// 主程序
function main() {
    log("=== 🎯 微信小程序广告自动刷脚本 ===", "SUCCESS");
    log("版本: v11.0 (完整流程版)");
    log("基于坐标: 观看广告(" + CONFIG.coordinates.watchAd.x + "," + CONFIG.coordinates.watchAd.y +
        ") → 关闭(" + CONFIG.coordinates.closeAd.x + "," + CONFIG.coordinates.closeAd.y +
        ") → 确认(" + CONFIG.coordinates.confirm.x + "," + CONFIG.coordinates.confirm.y + ")");

    try {
        // 1. 检查权限
        if (!checkPermissions()) {
            log("❌ 权限检查失败，脚本退出", "ERROR");
            return;
        }

        // 2. 显示设备信息
        log("📱 设备信息:");
        log("  - 品牌: " + device.brand);
        log("  - 型号: " + device.model);
        log("  - 分辨率: " + device.width + "x" + device.height);
        log("  - Android版本: " + device.release);

        // 3. 设置紧急停止
        setupEmergencyStop();

        // 4. 使用说明
        log("=== 📋 使用说明 ===");
        log("1. 请确保已手动打开微信中的抖音去水印小程序");
        log("2. 确保在小程序主界面，能看到广告按钮");
        log("3. 脚本将按照您提供的坐标自动执行");
        log("4. 紧急停止: 连按3次音量下键");
        log("5. 脚本将持续运行最多8小时，每轮间隔" + CONFIG.interval + "秒");
        log("6. 脚本会自动在8小时后停止，无需手动干预");

        // 5. 开始执行
        mainLoop();

        // 6. 显示最终结果
        showFinalResults();

    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);

        // 显示错误结果
        showFinalResults();
    } finally {
        // 清理事件监听
        try {
            events.removeAllListeners();
        } catch (e) {
            // 忽略清理错误
        }
    }
}


// 启动脚本
log("🚀 准备启动微信小程序广告自动刷脚本...");
log("请确保已在小程序界面，脚本即将开始...");
main();