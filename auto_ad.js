// ========================================
// 微信小程序广告自动刷脚本 - 基于坐标点击
// 根据用户提供的完整流程优化
// 版本：v11.0 (完整流程版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.8, device.height * 0.5);
console.setPosition(100, 200);

console.log("🚀 微信小程序广告自动刷脚本启动...");

// 配置参数
var CONFIG = {
    // 基础配置
    maxLoops: 50,              // 最大循环次数
    interval: 10,              // 每轮间隔时间（秒）
    adWaitTime: 35,            // 广告等待时间（秒）

    // 坐标配置（基于用户提供的坐标）
    coordinates: {
        watchAd: { x: 546, y: 1152 },      // 观看广告按钮
        closeAd: { x: 975, y: 85 },        // 关闭广告按钮
        confirm: { x: 588, y: 1366 }       // 确认按钮（太好了）
    },

    // 高级配置
    randomOffset: 15,          // 随机偏移范围
    retryTimes: 3,             // 失败重试次数
    safeMode: true,            // 安全模式（检查应用状态）
    logToFile: true            // 记录日志到文件
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;
var skipCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/wechat_miniprogram_ad_log.txt";

// 日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;

    // 控制台输出
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }

    // 文件日志
    if (CONFIG.logToFile) {
        try {
            files.append(LOG_FILE, logMsg + "\n");
        } catch (e) {
            // 忽略文件写入错误
        }
    }

    // Toast提示（重要信息）
    if (level === "SUCCESS" || level === "ERROR" || message.indexOf("完成") !== -1) {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");

    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            toast("请开启无障碍服务");
            return false;
        }
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 智能点击函数（带随机偏移和错误处理）
function smartClick(x, y, description, useOffset) {
    useOffset = useOffset !== false; // 默认使用偏移

    try {
        var actualX = x;
        var actualY = y;

        // 添加随机偏移，模拟真实点击
        if (useOffset) {
            actualX += random(-CONFIG.randomOffset, CONFIG.randomOffset);
            actualY += random(-CONFIG.randomOffset, CONFIG.randomOffset);
        }

        // 确保坐标在屏幕范围内
        actualX = Math.max(0, Math.min(device.width, actualX));
        actualY = Math.max(0, Math.min(device.height, actualY));

        log("👆 点击: (" + actualX + ", " + actualY + ") - " + description);

        click(actualX, actualY);

        log("✅ 点击成功: " + description, "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 安全等待函数
function safeWait(seconds, description) {
    description = description || "等待";
    log("⏳ " + description + " " + seconds + " 秒...");

    try {
        sleep(seconds * 1000);
        return true;
    } catch (e) {
        log("❌ 等待被中断: " + e.message, "ERROR");
        return false;
    }
}

// 检查当前应用状态
function checkAppStatus() {
    if (!CONFIG.safeMode) {
        return true;
    }

    try {
        var currentPkg = currentActivity();
        log("📱 当前应用: " + currentPkg);

        // 检查是否在微信中
        if (currentPkg === "com.tencent.mm") {
            return "wechat";
        }
        // 检查是否在广告应用中
        else if (currentPkg !== "com.tencent.mm") {
            return "ad_app";
        }

        return "unknown";
    } catch (e) {
        log("⚠️ 无法检查应用状态: " + e.message, "WARN");
        return "unknown";
    }
}

// 执行完整的广告流程
function executeAdFlow() {
    log("🎬 开始执行广告流程...");

    try {
        var coords = CONFIG.coordinates;

        // 步骤1: 点击"观看广告"按钮
        log("📍 步骤1: 点击观看广告按钮");
        if (!smartClick(coords.watchAd.x, coords.watchAd.y, "观看广告")) {
            log("❌ 点击观看广告失败", "ERROR");
            return false;
        }

        // 短暂等待广告加载
        safeWait(3, "等待广告加载");

        // 检查是否进入广告应用
        var appStatus = checkAppStatus();
        if (appStatus === "ad_app") {
            log("✅ 成功进入广告应用", "SUCCESS");
        } else {
            log("⚠️ 可能未进入广告应用，继续执行", "WARN");
        }

        // 步骤2: 等待广告播放完成
        log("📍 步骤2: 等待广告播放");
        safeWait(CONFIG.adWaitTime, "观看广告");

        // 步骤3: 点击关闭广告按钮
        log("📍 步骤3: 点击关闭广告按钮");
        if (!smartClick(coords.closeAd.x, coords.closeAd.y, "关闭广告")) {
            log("⚠️ 点击关闭广告失败，尝试返回键", "WARN");
            back();
            safeWait(2, "返回后等待");
        } else {
            safeWait(2, "关闭广告后等待");
        }

        // 步骤4: 点击确认按钮（太好了）
        log("📍 步骤4: 点击确认按钮");
        if (!smartClick(coords.confirm.x, coords.confirm.y, "确认按钮")) {
            log("⚠️ 点击确认按钮失败", "WARN");
        } else {
            safeWait(2, "确认后等待");
        }

        // 检查是否回到微信小程序
        appStatus = checkAppStatus();
        if (appStatus === "wechat") {
            log("✅ 成功回到微信小程序", "SUCCESS");
        }

        adCount++;
        successCount++;
        log("🎉 第" + adCount + "次广告流程完成", "SUCCESS");

        return true;

    } catch (e) {
        log("❌ 广告流程执行出错: " + e.message, "ERROR");
        errorCount++;
        return false;
    }
}

// 重试机制
function executeWithRetry() {
    for (var retry = 0; retry < CONFIG.retryTimes; retry++) {
        if (retry > 0) {
            log("🔄 第" + (retry + 1) + "次重试...");
            safeWait(3, "重试前等待");
        }

        if (executeAdFlow()) {
            return true;
        }

        log("❌ 第" + (retry + 1) + "次尝试失败");
    }

    log("❌ 所有重试都失败，跳过本轮", "ERROR");
    skipCount++;
    return false;
}

// 主循环
function mainLoop() {
    log("🚀 开始主循环", "SUCCESS");
    log("📋 配置信息:");
    log("  - 最大循环次数: " + CONFIG.maxLoops);
    log("  - 每轮间隔: " + CONFIG.interval + "秒");
    log("  - 广告等待时间: " + CONFIG.adWaitTime + "秒");
    log("  - 随机偏移范围: ±" + CONFIG.randomOffset + "像素");

    // 给用户准备时间
    log("⏰ 5秒后开始执行，请确保已在小程序界面...");
    safeWait(5, "准备时间");

    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("=== 📍 执行第 " + (i + 1) + "/" + CONFIG.maxLoops + " 轮 ===");

            // 执行广告流程（带重试）
            executeWithRetry();

            // 显示当前统计
            log("📊 当前统计: 成功" + successCount + "次, 失败" + errorCount + "次, 跳过" + skipCount + "次");

            // 等待下一轮
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒后继续下一轮...");
                safeWait(CONFIG.interval, "轮次间隔");
            }

        } catch (e) {
            log("❌ 主循环出错: " + e.message, "ERROR");
            errorCount++;
            safeWait(5, "错误恢复等待");
        }
    }

    log("🏁 主循环执行完成", "SUCCESS");
}

// 显示最终结果
function showFinalResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;

    log("=== 📊 脚本执行完成 - 最终统计 ===", "SUCCESS");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总尝试次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    log("⏭️ 跳过次数: " + skipCount + "次");

    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }

    // 计算效率
    if (totalTime > 0) {
        var adsPerMinute = Math.round((successCount / totalTime) * 60);
        log("⚡ 效率: " + adsPerMinute + "次/分钟");
    }

    log("📁 详细日志已保存到: " + LOG_FILE);

    // 最终提示
    var finalMessage = "脚本执行完成！成功" + successCount + "次，失败" + errorCount + "次";
    toast(finalMessage);

    // 保持悬浮窗显示结果
    log("悬浮窗将保持显示15秒...");
    safeWait(15, "结果显示");
}

// 紧急停止功能
function setupEmergencyStop() {
    log("🛑 紧急停止: 音量下键连按3次可停止脚本");

    var volumeDownCount = 0;
    var lastVolumeDownTime = 0;

    events.observeKey();
    events.onKeyDown("volume_down", function (event) {
        var currentTime = new Date().getTime();

        if (currentTime - lastVolumeDownTime < 1000) {
            volumeDownCount++;
        } else {
            volumeDownCount = 1;
        }

        lastVolumeDownTime = currentTime;

        if (volumeDownCount >= 3) {
            log("🛑 检测到紧急停止信号，脚本即将停止", "WARN");
            toast("紧急停止！脚本即将退出");
            showFinalResults();
            exit();
        }
    });
}

// 主程序
function main() {
    log("=== 🎯 微信小程序广告自动刷脚本 ===", "SUCCESS");
    log("版本: v11.0 (完整流程版)");
    log("基于坐标: 观看广告(" + CONFIG.coordinates.watchAd.x + "," + CONFIG.coordinates.watchAd.y +
        ") → 关闭(" + CONFIG.coordinates.closeAd.x + "," + CONFIG.coordinates.closeAd.y +
        ") → 确认(" + CONFIG.coordinates.confirm.x + "," + CONFIG.coordinates.confirm.y + ")");

    try {
        // 1. 检查权限
        if (!checkPermissions()) {
            log("❌ 权限检查失败，脚本退出", "ERROR");
            return;
        }

        // 2. 显示设备信息
        log("📱 设备信息:");
        log("  - 品牌: " + device.brand);
        log("  - 型号: " + device.model);
        log("  - 分辨率: " + device.width + "x" + device.height);
        log("  - Android版本: " + device.release);

        // 3. 设置紧急停止
        setupEmergencyStop();

        // 4. 使用说明
        log("=== 📋 使用说明 ===");
        log("1. 请确保已手动打开微信中的抖音去水印小程序");
        log("2. 确保在小程序主界面，能看到广告按钮");
        log("3. 脚本将按照您提供的坐标自动执行");
        log("4. 紧急停止: 连按3次音量下键");
        log("5. 脚本将执行" + CONFIG.maxLoops + "轮，每轮间隔" + CONFIG.interval + "秒");

        // 5. 开始执行
        mainLoop();

        // 6. 显示最终结果
        showFinalResults();

    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);

        // 显示错误结果
        showFinalResults();
    } finally {
        // 清理事件监听
        try {
            events.removeAllListeners();
        } catch (e) {
            // 忽略清理错误
        }
    }
}

// 启动脚本
log("🚀 准备启动微信小程序广告自动刷脚本...");
log("请确保已在小程序界面，脚本即将开始...");
main();