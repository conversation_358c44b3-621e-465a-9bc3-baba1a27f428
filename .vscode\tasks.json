{"version": "2.0.0", "tasks": [{"label": "运行手机端脚本", "type": "shell", "command": "autojs", "args": ["run", "${workspaceFolder}/抖音去水印小程序广告自动刷脚本.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "运行电脑端脚本", "type": "shell", "command": "node", "args": ["${workspaceFolder}/电脑端抖音广告自动刷脚本.js"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "测试ADB连接", "type": "shell", "command": "node", "args": ["${workspaceFolder}/test-adb.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "坐标调试工具", "type": "shell", "command": "node", "args": ["${workspaceFolder}/坐标调试工具.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "快速测试", "type": "shell", "command": "${workspaceFolder}/快速测试.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}