{"version": "0.2.0", "configurations": [{"name": "Auto.js: 运行手机端脚本", "type": "autojs", "request": "launch", "program": "${workspaceFolder}/抖音去水印小程序广告自动刷脚本.js", "device": "usb", "console": "integratedTerminal"}, {"name": "Auto.js: 调试手机端脚本", "type": "autojs", "request": "launch", "program": "${workspaceFolder}/抖音去水印小程序广告自动刷脚本.js", "device": "usb", "console": "integratedTerminal", "stopOnEntry": true}, {"name": "Node.js: 运行电脑端脚本", "type": "node", "request": "launch", "program": "${workspaceFolder}/电脑端抖音广告自动刷脚本.js", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}}, {"name": "Node.js: 调试电脑端脚本", "type": "node", "request": "launch", "program": "${workspaceFolder}/电脑端抖音广告自动刷脚本.js", "console": "integratedTerminal", "stopOnEntry": true, "env": {"NODE_ENV": "development"}}]}