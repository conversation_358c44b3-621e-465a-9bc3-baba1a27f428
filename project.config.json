{"description": "项目配置文件", "cloudfunctionRoot": "cloud/", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": false, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false}, "compileType": "miniprogram", "libVersion": "3.8.10", "appid": "wxb6ca00ed0378c797", "projectname": "douyin-miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}