[GMT+08:00 下午7:49:39] === 抖音去水印小程序界面调试脚本 ===
[GMT+08:00 下午7:49:39] 检查权限...
[GMT+08:00 下午7:49:39] 权限检查通过
[GMT+08:00 下午7:49:39] 设备品牌: HUAWEI
[GMT+08:00 下午7:49:39] 设备型号: LYA-AL00
[GMT+08:00 下午7:49:39] 屏幕分辨率: 1080x2340
[GMT+08:00 下午7:49:39] === 调试说明 ===
[GMT+08:00 下午7:49:39] 1. 请确保已在目标小程序界面
[GMT+08:00 下午7:49:39] 2. 脚本将分析当前界面的所有元素
[GMT+08:00 下午7:49:39] 3. 查看日志了解界面结构
[GMT+08:00 下午7:49:42] === 开始分析当前界面 ===
[GMT+08:00 下午7:49:42] 方法1: 查找所有文本节点
[GMT+08:00 下午7:49:42] 方法1失败: Can't find method com.stardust.automator.UiGlobalSelector.text(). (file:///android_asset/modules/__selector__.js#12)
[GMT+08:00 下午7:49:42] 方法2: 查找包含特定文本的节点
[GMT+08:00 下午7:49:42] 方法3: 查找可点击元素
[GMT+08:00 下午7:49:42] 找到可点击元素数量: 0
[GMT+08:00 下午7:49:42] 方法4: 分析根节点
[GMT+08:00 下午7:49:43] 方法5: 查找小程序特征元素
[GMT+08:00 下午7:49:43] === 界面分析完成 ===
[GMT+08:00 下午7:49:43] === 查找广告相关元素 ===
[GMT+08:00 下午7:49:43] === 广告元素查找完成 ===
[GMT+08:00 下午7:49:43] 调试完成，请查看日志文件
