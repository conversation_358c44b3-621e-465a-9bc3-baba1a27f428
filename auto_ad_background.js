// ========================================
// 抖音去水印小程序广告自动刷脚本 - 后台运行版
// 解决切换应用后无法识别界面的问题
// 版本：v5.0 (后台运行版)
// ========================================

console.log("后台运行版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 30,              // 每次操作间隔30秒
    adWaitTime: 25000,         // 广告等待时间25秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 3000,         // 等待超时时间3秒
    maxLoops: 20,              // 最大循环次数
    autoSwitchApp: true,       // 自动切换应用
    backgroundMode: true       // 后台模式
};

// 广告按钮关键词
var AD_KEYWORDS = [
    "观看广告获取金币",
    "观看广告",
    "看广告",
    "广告",
    "观看视频",
    "看视频",
    "视频广告",
    "激励视频",
    "获取奖励",
    "领取奖励",
    "获得金币",
    "赚取金币",
    "免费领取",
    "金币",
    "奖励",
    "免费",
    "观看",
    "视频"
];

// 关闭按钮关键词
var CLOSE_KEYWORDS = [
    "关闭",
    "跳过",
    "×",
    "X",
    "确定",
    "完成",
    "知道了",
    "好的",
    "继续"
];

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_background_log.txt";

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    // 后台模式下减少toast频率
    if (!CONFIG.backgroundMode || message.indexOf("成功") !== -1 || message.indexOf("失败") !== -1) {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务");
            toast("请开启无障碍服务");
            return false;
        }
        log("✅ 权限检查通过");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message);
        return false;
    }
}

// 切换到微信应用
function switchToWechat() {
    log("切换到微信应用...");
    
    try {
        // 方法1: 使用app.launch启动微信
        app.launch("com.tencent.mm");
        sleep(3000);
        
        // 检查是否成功切换
        var currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 成功切换到微信");
            return true;
        }
        
        // 方法2: 使用app.startActivity
        app.startActivity({
            packageName: "com.tencent.mm",
            className: "com.tencent.mm.ui.LauncherUI"
        });
        sleep(3000);
        
        currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 成功切换到微信");
            return true;
        }
        
        log("❌ 切换到微信失败");
        return false;
        
    } catch (e) {
        log("❌ 切换到微信出错: " + e.message);
        return false;
    }
}

// 检查是否在目标界面
function checkTargetInterface() {
    try {
        // 检查当前应用
        var currentPkg = currentPackage();
        if (currentPkg !== "com.tencent.mm") {
            log("当前不在微信中: " + currentPkg);
            return false;
        }
        
        // 检查是否有小程序特征
        var indicators = [
            "抖音", "去水印", "下载", "解析", "金币", "观看", "广告"
        ];
        
        for (var i = 0; i < indicators.length; i++) {
            var found = textContains(indicators[i]).findOne(1000);
            if (found) {
                log("✅ 检测到小程序特征: " + indicators[i]);
                return true;
            }
        }
        
        log("⚠️ 未检测到小程序特征");
        return false;
        
    } catch (e) {
        log("❌ 检查目标界面出错: " + e.message);
        return false;
    }
}

// 智能查找广告按钮
function findAdButton() {
    try {
        // 多种方法查找广告按钮
        for (var i = 0; i < AD_KEYWORDS.length; i++) {
            var keyword = AD_KEYWORDS[i];
            
            // 精确文本匹配
            var element = text(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(精确): " + keyword);
                return element;
            }
            
            // 包含文本匹配
            element = textContains(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(包含): " + keyword + " -> " + element.text());
                return element;
            }
            
            // 描述匹配
            element = desc(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(描述): " + keyword);
                return element;
            }
            
            element = descContains(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(包含描述): " + keyword);
                return element;
            }
        }
        
        return null;
        
    } catch (e) {
        log("❌ 查找广告按钮出错: " + e.message);
        return null;
    }
}

// 查找关闭按钮
function findCloseButton() {
    try {
        for (var i = 0; i < CLOSE_KEYWORDS.length; i++) {
            var keyword = CLOSE_KEYWORDS[i];
            
            var element = text(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到关闭按钮: " + keyword);
                return element;
            }
            
            element = textContains(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到关闭按钮(包含): " + keyword);
                return element;
            }
        }
        
        return null;
        
    } catch (e) {
        log("❌ 查找关闭按钮出错: " + e.message);
        return null;
    }
}

// 安全点击
function safeClick(element, description) {
    if (!element) {
        log("元素不存在: " + description);
        return false;
    }
    
    try {
        if (!element.clickable()) {
            log("元素不可点击: " + description);
            return false;
        }
        
        element.click();
        log("✅ 点击成功: " + description);
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message);
        return false;
    }
}

// 处理广告流程
function handleAdProcess() {
    log("开始处理广告流程...");
    
    try {
        // 1. 确保在正确界面
        if (!checkTargetInterface()) {
            if (CONFIG.autoSwitchApp) {
                log("尝试切换到微信...");
                if (!switchToWechat()) {
                    return false;
                }
                sleep(2000);
                
                if (!checkTargetInterface()) {
                    log("❌ 切换后仍未检测到目标界面");
                    return false;
                }
            } else {
                log("❌ 不在目标界面");
                return false;
            }
        }
        
        // 2. 查找广告按钮
        var adBtn = findAdButton();
        if (!adBtn) {
            log("❌ 未找到广告按钮");
            return false;
        }
        
        // 3. 点击广告按钮
        if (!safeClick(adBtn, "广告按钮")) {
            return false;
        }
        
        adCount++;
        log("🎬 第" + adCount + "次广告开始");
        
        // 4. 等待广告加载
        sleep(3000);
        
        // 5. 等待广告播放
        log("⏳ 等待广告播放...");
        sleep(15000);
        
        // 6. 尝试关闭广告
        var closeBtn = findCloseButton();
        if (closeBtn) {
            if (safeClick(closeBtn, "关闭按钮")) {
                sleep(2000);
                successCount++;
                log("✅ 第" + adCount + "次广告完成");
                return true;
            }
        } else {
            log("⚠️ 未找到关闭按钮，尝试返回");
            back();
            sleep(2000);
        }
        
        return true;
        
    } catch (e) {
        log("❌ 处理广告流程出错: " + e.message);
        return false;
    }
}

// 主循环
function mainLoop() {
    log("=== 🚀 开始后台运行版循环 ===");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("📍 执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 处理广告
            if (handleAdProcess()) {
                log("✅ 广告处理成功");
            } else {
                log("❌ 广告处理失败");
                errorCount++;
            }
            
            // 检查错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("❌ 循环出错: " + e.message);
            
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本");
                break;
            }
            
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    
    log("=== 📊 后台运行版脚本执行完成 ===");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总广告次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }
    
    toast("脚本执行完成！成功" + successCount + "次，失败" + errorCount + "次");
}

// 主程序
function main() {
    log("=== 🎯 抖音去水印小程序广告脚本 - 后台运行版 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("❌ 权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("📱 设备品牌: " + device.brand);
        log("📱 设备型号: " + device.model);
        log("📱 屏幕分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 📋 后台运行版使用说明 ===");
        log("1. 脚本将在后台自动运行");
        log("2. 自动切换到微信小程序");
        log("3. 可以切换到其他应用而不影响运行");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 查看日志文件: " + LOG_FILE);
        
        // 延迟启动
        sleep(3000);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
    } catch (e) {
        log("❌ 主程序出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
}

// 启动脚本
console.log("准备启动后台运行版脚本...");
main();
