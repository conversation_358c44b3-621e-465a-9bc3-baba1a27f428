// ========================================
// 抖音去水印小程序广告自动刷脚本 - 坐标点击版
// 针对微信小程序无节点信息的解决方案
// 版本：v8.0 (坐标点击版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.9, device.height * 0.6);
console.setPosition(50, 100);

console.log("🚀 坐标点击版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 30,              // 每次操作间隔30秒
    adWaitTime: 25000,         // 广告等待时间25秒
    maxRetries: 3,             // 最大重试次数
    maxLoops: 15,              // 最大循环次数
    screenWidth: device.width,
    screenHeight: device.height,
    coordinateMode: true       // 坐标模式
};

// 不同分辨率的坐标配置
var COORDINATES = {
    // 1080x2340 (常见分辨率)
    "1080x2340": {
        adButton: {x: 540, y: 1800},      // 广告按钮位置
        closeButton: {x: 540, y: 300},     // 关闭按钮位置
        backButton: {x: 100, y: 150}       // 返回按钮位置
    },
    // 1080x2400
    "1080x2400": {
        adButton: {x: 540, y: 1900},
        closeButton: {x: 540, y: 300},
        backButton: {x: 100, y: 150}
    },
    // 720x1560
    "720x1560": {
        adButton: {x: 360, y: 1200},
        closeButton: {x: 360, y: 200},
        backButton: {x: 70, y: 100}
    },
    // 默认配置（按比例计算）
    "default": {
        adButton: {x: 0.5, y: 0.8},       // 屏幕中下部
        closeButton: {x: 0.5, y: 0.15},   // 屏幕上部中央
        backButton: {x: 0.1, y: 0.1}      // 左上角
    }
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;
var currentCoords = null;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_coordinate_log.txt";

// 日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;
    
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    if (level === "SUCCESS" || level === "ERROR") {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            return false;
        }
        
        // 检查屏幕录制权限
        if (!requestScreenCapture()) {
            log("❌ 需要屏幕录制权限", "ERROR");
            return false;
        }
        
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 获取当前分辨率对应的坐标配置
function getCoordinates() {
    var resolution = CONFIG.screenWidth + "x" + CONFIG.screenHeight;
    log("📱 当前分辨率: " + resolution);
    
    if (COORDINATES[resolution]) {
        log("✅ 找到匹配的坐标配置");
        return COORDINATES[resolution];
    } else {
        log("⚠️ 使用默认坐标配置（按比例计算）", "WARN");
        var defaultCoords = COORDINATES["default"];
        return {
            adButton: {
                x: Math.floor(CONFIG.screenWidth * defaultCoords.adButton.x),
                y: Math.floor(CONFIG.screenHeight * defaultCoords.adButton.y)
            },
            closeButton: {
                x: Math.floor(CONFIG.screenWidth * defaultCoords.closeButton.x),
                y: Math.floor(CONFIG.screenHeight * defaultCoords.closeButton.y)
            },
            backButton: {
                x: Math.floor(CONFIG.screenWidth * defaultCoords.backButton.x),
                y: Math.floor(CONFIG.screenHeight * defaultCoords.backButton.y)
            }
        };
    }
}

// 切换到微信
function switchToWechat() {
    log("📱 切换到微信...");
    
    try {
        app.launch("com.tencent.mm");
        sleep(3000);
        
        var currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 成功切换到微信", "SUCCESS");
            return true;
        } else {
            log("❌ 切换失败，当前应用: " + currentPkg, "ERROR");
            return false;
        }
    } catch (e) {
        log("❌ 切换到微信出错: " + e.message, "ERROR");
        return false;
    }
}

// 坐标点击函数
function clickCoordinate(x, y, description) {
    try {
        // 添加随机偏移，模拟真实点击
        var offsetX = x + random(-10, 10);
        var offsetY = y + random(-10, 10);
        
        // 确保坐标在屏幕范围内
        offsetX = Math.max(0, Math.min(CONFIG.screenWidth, offsetX));
        offsetY = Math.max(0, Math.min(CONFIG.screenHeight, offsetY));
        
        log("👆 点击坐标: (" + offsetX + ", " + offsetY + ") - " + description);
        
        click(offsetX, offsetY);
        sleep(1000);
        
        log("✅ 坐标点击成功: " + description, "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 坐标点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 截图分析（可选功能）
function analyzeScreen() {
    log("📸 分析当前屏幕...");
    
    try {
        var img = captureScreen();
        if (!img) {
            log("❌ 截图失败", "ERROR");
            return false;
        }
        
        log("✅ 截图成功: " + img.width + "x" + img.height);
        
        // 这里可以添加图像识别逻辑
        // 例如：检测特定颜色、形状等
        
        img.recycle(); // 释放图片内存
        return true;
    } catch (e) {
        log("❌ 屏幕分析失败: " + e.message, "ERROR");
        return false;
    }
}

// 多点尝试点击广告按钮
function tryClickAdButton() {
    log("🎯 尝试点击广告按钮...");
    
    var adPositions = [
        // 主要位置
        {x: currentCoords.adButton.x, y: currentCoords.adButton.y, desc: "主要广告位置"},
        
        // 备选位置（屏幕下方不同位置）
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.75, desc: "屏幕下方中央"},
        {x: CONFIG.screenWidth * 0.3, y: CONFIG.screenHeight * 0.8, desc: "屏幕下方左侧"},
        {x: CONFIG.screenWidth * 0.7, y: CONFIG.screenHeight * 0.8, desc: "屏幕下方右侧"},
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.85, desc: "屏幕底部中央"},
        
        // 屏幕中央区域
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.6, desc: "屏幕中央"},
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.65, desc: "屏幕中下部"}
    ];
    
    for (var i = 0; i < adPositions.length; i++) {
        var pos = adPositions[i];
        log("🎯 尝试位置 " + (i + 1) + ": " + pos.desc);
        
        if (clickCoordinate(pos.x, pos.y, pos.desc)) {
            sleep(2000);
            
            // 检查是否成功进入广告
            var currentPkg = currentPackage();
            if (currentPkg !== "com.tencent.mm") {
                log("✅ 可能进入了广告应用: " + currentPkg, "SUCCESS");
                return true;
            }
        }
        
        sleep(1000);
    }
    
    log("❌ 所有广告位置都尝试失败", "WARN");
    return false;
}

// 多点尝试关闭广告
function tryCloseAd() {
    log("❌ 尝试关闭广告...");
    
    var closePositions = [
        // 右上角关闭按钮
        {x: CONFIG.screenWidth * 0.9, y: CONFIG.screenHeight * 0.1, desc: "右上角关闭"},
        {x: CONFIG.screenWidth * 0.85, y: CONFIG.screenHeight * 0.15, desc: "右上角关闭2"},
        
        // 左上角返回按钮
        {x: CONFIG.screenWidth * 0.1, y: CONFIG.screenHeight * 0.1, desc: "左上角返回"},
        
        // 屏幕上方中央
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.1, desc: "上方中央"},
        {x: CONFIG.screenWidth * 0.5, y: CONFIG.screenHeight * 0.15, desc: "上方中央2"},
        
        // 配置的关闭按钮位置
        {x: currentCoords.closeButton.x, y: currentCoords.closeButton.y, desc: "配置关闭位置"}
    ];
    
    for (var i = 0; i < closePositions.length; i++) {
        var pos = closePositions[i];
        log("❌ 尝试关闭位置 " + (i + 1) + ": " + pos.desc);
        
        if (clickCoordinate(pos.x, pos.y, pos.desc)) {
            sleep(2000);
            
            // 检查是否回到微信
            var currentPkg = currentPackage();
            if (currentPkg === "com.tencent.mm") {
                log("✅ 成功关闭广告，回到微信", "SUCCESS");
                return true;
            }
        }
        
        sleep(1000);
    }
    
    // 尝试返回键
    log("🔙 尝试使用返回键...");
    back();
    sleep(2000);
    
    var currentPkg = currentPackage();
    if (currentPkg === "com.tencent.mm") {
        log("✅ 返回键成功关闭广告", "SUCCESS");
        return true;
    }
    
    log("❌ 所有关闭方式都失败", "WARN");
    return false;
}

// 处理广告流程
function handleAdProcess() {
    log("🎬 开始处理广告流程...");
    
    try {
        // 1. 确保在微信中
        if (!switchToWechat()) {
            return false;
        }
        
        // 2. 可选：分析屏幕
        analyzeScreen();
        
        // 3. 尝试点击广告按钮
        if (!tryClickAdButton()) {
            log("❌ 未能点击广告按钮", "WARN");
            return false;
        }
        
        adCount++;
        log("🎬 第" + adCount + "次广告开始", "SUCCESS");
        
        // 4. 等待广告播放
        log("⏳ 等待广告播放" + (CONFIG.adWaitTime/1000) + "秒...");
        sleep(CONFIG.adWaitTime);
        
        // 5. 尝试关闭广告
        if (tryCloseAd()) {
            successCount++;
            log("✅ 第" + adCount + "次广告完成", "SUCCESS");
            return true;
        } else {
            log("⚠️ 广告关闭可能失败", "WARN");
            // 即使关闭失败也算一次尝试
            return true;
        }
        
    } catch (e) {
        log("❌ 处理广告流程出错: " + e.message, "ERROR");
        return false;
    }
}

// 主循环
function mainLoop() {
    log("🚀 开始坐标点击版主循环", "SUCCESS");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("📍 执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 处理广告
            if (handleAdProcess()) {
                log("✅ 广告处理成功");
                errorCount = 0; // 重置错误计数
            } else {
                log("❌ 广告处理失败");
                errorCount++;
            }
            
            // 检查连续错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 连续错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("❌ 循环出错: " + e.message, "ERROR");
            
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    
    log("=== 📊 坐标点击版脚本执行完成 ===", "SUCCESS");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总广告次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }
    
    toast("脚本执行完成！成功" + successCount + "次");
}

// 主程序
function main() {
    log("=== 🎯 抖音去水印小程序广告脚本 - 坐标点击版 ===", "SUCCESS");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            return;
        }
        
        // 获取坐标配置
        currentCoords = getCoordinates();
        log("📍 广告按钮坐标: (" + currentCoords.adButton.x + ", " + currentCoords.adButton.y + ")");
        log("📍 关闭按钮坐标: (" + currentCoords.closeButton.x + ", " + currentCoords.closeButton.y + ")");
        
        // 显示设备信息
        log("📱 设备: " + device.brand + " " + device.model);
        log("📱 分辨率: " + CONFIG.screenWidth + "x" + CONFIG.screenHeight);
        
        // 使用说明
        log("=== 📋 坐标点击版说明 ===");
        log("1. 使用坐标点击，不依赖节点信息");
        log("2. 支持多个位置尝试点击");
        log("3. 适用于微信小程序环境");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 日志保存到: " + LOG_FILE);
        
        // 延迟启动
        sleep(3000);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
        // 保持悬浮窗显示
        sleep(10000);
        
    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);
    }
}

// 启动脚本
log("🚀 准备启动坐标点击版脚本...");
main();
