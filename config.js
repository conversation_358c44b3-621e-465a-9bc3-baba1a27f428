// ========================================
// 微信小程序广告自动刷脚本 - 配置文件
// 所有可调整的参数都在这里
// 版本：v11.0
// ========================================

// 🎯 主要配置参数 - 根据需要修改这些值
var USER_CONFIG = {
    
    // ⏰ 时间控制配置
    TIME_SETTINGS: {
        maxRunTimeHours: 8,        // 最大运行时间（小时）
        intervalMinSec: 15,        // 每轮最短间隔时间（秒）
        intervalMaxSec: 35,        // 每轮最长间隔时间（秒）
        adWaitMinSec: 32,          // 广告观看最短时间（秒）
        adWaitMaxSec: 45,          // 广告观看最长时间（秒）
        longBreakMinSec: 180,      // 长休息最短时间（秒，3分钟）
        longBreakMaxSec: 300       // 长休息最长时间（秒，5分钟）
    },
    
    // 🛡️ 防封号限制配置
    SAFETY_LIMITS: {
        maxContinuousAds: 15,      // 连续广告最大次数（超过后强制休息）
        dailyAdLimit: 200,         // 每日广告总数限制
        randomBreakChance: 0.1,    // 随机休息概率（0.1 = 10%）
        minAdsBeforeRandomBreak: 5 // 至少多少次广告后才可能随机休息
    },
    
    // 🎯 坐标配置
    COORDINATES: {
        watchAd: { x: 546, y: 1152 },    // 观看广告按钮坐标
        closeAd: { x: 975, y: 85 },      // 关闭广告按钮坐标
        confirm: { x: 588, y: 1366 }     // 确认按钮坐标（太好了）
    },
    
    // 🎲 人性化行为配置
    HUMAN_BEHAVIOR: {
        enabled: true,             // 是否启用人性化行为模拟
        randomOffset: 15,          // 点击随机偏移范围（像素）
        clickPrePauseMin: 200,     // 点击前最短停顿时间（毫秒）
        clickPrePauseMax: 800,     // 点击前最长停顿时间（毫秒）
        clickPostPauseMin: 300,    // 点击后最短停顿时间（毫秒）
        clickPostPauseMax: 1000,   // 点击后最长停顿时间（毫秒）
        pressDurationMin: 50,      // 按压最短时间（毫秒）
        pressDurationMax: 150      // 按压最长时间（毫秒）
    },
    
    // 📊 日志配置
    LOG_SETTINGS: {
        enableFileLog: true,       // 是否保存日志到文件
        logFilePath: "/sdcard/wechat_miniprogram_ad_log.txt",
        showDetailedStats: true,   // 是否显示详细统计
        statsUpdateInterval: 30    // 统计更新间隔（分钟）
    },
    
    // 🔧 高级配置
    ADVANCED: {
        retryTimes: 3,             // 失败重试次数
        safeMode: true,            // 安全模式（检查应用状态）
        emergencyStopEnabled: true // 紧急停止功能（音量键）
    }
};

// ========================================
// 🔄 配置转换函数 - 不要修改这部分
// ========================================

// 将用户配置转换为脚本内部使用的CONFIG格式
function convertToInternalConfig() {
    return {
        // 基础配置
        maxLoops: 999,
        intervalMin: USER_CONFIG.TIME_SETTINGS.intervalMinSec,
        intervalMax: USER_CONFIG.TIME_SETTINGS.intervalMaxSec,
        adWaitTimeMin: USER_CONFIG.TIME_SETTINGS.adWaitMinSec,
        adWaitTimeMax: USER_CONFIG.TIME_SETTINGS.adWaitMaxSec,
        maxRunTime: USER_CONFIG.TIME_SETTINGS.maxRunTimeHours * 60 * 60,
        
        // 防封号配置
        maxContinuousAds: USER_CONFIG.SAFETY_LIMITS.maxContinuousAds,
        longBreakMin: USER_CONFIG.TIME_SETTINGS.longBreakMinSec,
        longBreakMax: USER_CONFIG.TIME_SETTINGS.longBreakMaxSec,
        dailyAdLimit: USER_CONFIG.SAFETY_LIMITS.dailyAdLimit,
        randomBreakChance: USER_CONFIG.SAFETY_LIMITS.randomBreakChance,
        minAdsBeforeRandomBreak: USER_CONFIG.SAFETY_LIMITS.minAdsBeforeRandomBreak,
        
        // 坐标配置
        coordinates: USER_CONFIG.COORDINATES,
        
        // 人性化行为配置
        humanBehavior: USER_CONFIG.HUMAN_BEHAVIOR.enabled,
        randomOffset: USER_CONFIG.HUMAN_BEHAVIOR.randomOffset,
        clickPrePauseMin: USER_CONFIG.HUMAN_BEHAVIOR.clickPrePauseMin,
        clickPrePauseMax: USER_CONFIG.HUMAN_BEHAVIOR.clickPrePauseMax,
        clickPostPauseMin: USER_CONFIG.HUMAN_BEHAVIOR.clickPostPauseMin,
        clickPostPauseMax: USER_CONFIG.HUMAN_BEHAVIOR.clickPostPauseMax,
        pressDurationMin: USER_CONFIG.HUMAN_BEHAVIOR.pressDurationMin,
        pressDurationMax: USER_CONFIG.HUMAN_BEHAVIOR.pressDurationMax,
        
        // 日志配置
        logToFile: USER_CONFIG.LOG_SETTINGS.enableFileLog,
        logFilePath: USER_CONFIG.LOG_SETTINGS.logFilePath,
        showDetailedStats: USER_CONFIG.LOG_SETTINGS.showDetailedStats,
        
        // 高级配置
        retryTimes: USER_CONFIG.ADVANCED.retryTimes,
        safeMode: USER_CONFIG.ADVANCED.safeMode,
        emergencyStopEnabled: USER_CONFIG.ADVANCED.emergencyStopEnabled
    };
}

// ========================================
// 📋 配置说明和建议值
// ========================================

/*
🎯 配置参数说明：

⏰ 时间设置建议：
- maxRunTimeHours: 建议6-8小时，避免过长时间运行
- intervalMinSec/MaxSec: 建议15-35秒，模拟真实用户间隔
- adWaitMinSec/MaxSec: 建议32-45秒，确保广告完整播放
- longBreakMinSec/MaxSec: 建议180-300秒，模拟用户休息

🛡️ 安全限制建议：
- maxContinuousAds: 建议10-20次，避免过度连续
- dailyAdLimit: 建议150-250次，根据平台限制调整
- randomBreakChance: 建议0.05-0.15（5%-15%）
- minAdsBeforeRandomBreak: 建议3-8次

🎯 坐标设置：
- 根据您的设备分辨率和小程序界面调整
- 可以使用coordinate_finder.js工具获取精确坐标

🎲 人性化行为：
- enabled: 强烈建议开启，大大降低被检测风险
- randomOffset: 建议10-20像素
- 各种停顿时间建议保持默认值

📊 日志设置：
- enableFileLog: 建议开启，便于分析和调试
- showDetailedStats: 建议开启，了解运行状态

🔧 高级设置：
- retryTimes: 建议2-5次
- safeMode: 建议开启
- emergencyStopEnabled: 建议开启

⚠️ 重要提醒：
1. 首次使用建议设置较保守的参数
2. 根据实际效果逐步调整
3. 定期更换参数，避免固定模式
4. 注意观察是否有异常，及时调整
*/
