/**index.wxss**/
page {
  background-color: #f7f7f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  box-sizing: border-box;
  padding-bottom: 20px;
}

.header {
  background-color: #07c160;
  color: white;
  text-align: center;
  padding: 15px 0;
  width: 100%;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.15);
  position: relative;
}

.header .title {
  font-size: 20px;
  font-weight: bold;
}

.coins-display {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 15px;
}

.coins-icon {
  font-size: 16px;
  margin-right: 5px;
}

.coins-text {
  font-size: 14px;
  font-weight: bold;
}

.content {
  flex: 1;
  width: 90%;
  margin: 0 auto;
  padding: 20px 0;
}

/* 删除图标区域样式 */

.title-area {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.main-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.sub-title {
  font-size: 14px;
  color: #777;
}

.input-group {
  margin-bottom: 20px;
}

.input-area {
  display: flex;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 0 0 12px;
  margin-bottom: 15px;
  overflow: hidden;
}

.url-input {
  flex: 1;
  height: 44px;
  font-size: 14px;
  color: #333;
}

.paste-btn {
  width: 70px;
  height: 44px;
  background-color: #f0f0f0;
  color: #333;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #e0e0e0;
}

/* 删除不需要的样式 */

.action-btn {
  width: 100%;
  background-color: #07c160;
  color: white;
  font-size: 16px;
  padding: 10px 0;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
  margin-bottom: 10px;
}

.ad-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  color: white;
  font-size: 15px;
  padding: 10px 0;
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.ad-btn[disabled] {
  background: #cccccc;
  color: #999999;
  box-shadow: none;
}

.ad-icon {
  font-size: 18px;
  margin-right: 8px;
}

.ad-text {
  font-size: 15px;
}

/* 结果区域样式 */
.result-section {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background-color: #07c160;
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
}

.video-container {
  width: 100%;
  background-color: #f0f0f0;
}

.video-container video,
.video-container swiper {
  width: 100%;
  height: 210px;
}

.video-container image {
  width: 100%;
  height: 100%;
}

.video-info {
  padding: 15px;
}

.video-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.author {
  display: flex;
  align-items: center;
}

.author image {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.author text {
  font-size: 14px;
  color: #666;
}

.result-actions {
  display: flex;
  border-top: 1px solid #eee;
  padding: 10px 15px;
}

.result-actions button {
  flex: 1;
  margin: 0 5px;
  font-size: 14px;
  padding: 8px 0;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #666;
  border: none;
  line-height: 1.5;
}

.result-actions button.primary {
  background-color: #07c160;
  color: white;
}

.divider {
  position: relative;
  height: 30px;
  text-align: center;
  margin: 25px 0 15px;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e0e0e0;
}

.divider-text {
  position: relative;
  display: inline-block;
  padding: 0 10px;
  background-color: #f7f7f7;
  font-size: 14px;
  color: #999;
  z-index: 1;
}

.steps {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.step {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.step:last-child {
  margin-bottom: 0;
}

.step-num {
  width: 24px;
  height: 24px;
  background-color: #07c160;
  border-radius: 50%;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.error {
  margin-top: 15px;
  padding: 10px;
  color: #f44336;
  background-color: #ffebee;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

.footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 20px;
}

/* 加载动画 */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40px;
  height: 40px;
  border: 3px solid #e0e0e0;
  border-top-color: #07c160;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

.loading-text {
  margin-top: 15px;
  color: #666;
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 图标补充 */
.icon-link:before {
  content: "\e65e";
}

.icon-clear:before {
  content: "\e63f";
}

.icon-help:before {
  content: "\e67a";
}

.icon-error:before {
  content: "\e6cf";
}

.icon-close:before {
  content: "\e660";
}