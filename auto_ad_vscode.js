// ========================================
// 抖音去水印小程序广告自动刷脚本 - VSCode版
// 专门针对VSCode连接环境优化
// 版本：v4.0 (VSCode优化版)
// ========================================

console.log("VSCode优化版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 20,              // 每次操作间隔20秒
    adWaitTime: 25000,         // 广告等待时间25秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 2000,         // 等待超时时间2秒
    maxLoops: 10,              // 最大循环次数
    debugMode: true            // 调试模式
};

// 广告按钮关键词 - 扩展版
var AD_KEYWORDS = [
    // 完整匹配
    "观看广告获取金币",
    "观看广告",
    "看广告",
    "广告",
    "观看视频",
    "看视频",
    "视频广告",
    "激励视频",
    "获取奖励",
    "领取奖励",
    "获得金币",
    "赚取金币",
    "免费领取",
    "免费获得",
    "观看获得",
    "看广告获得",
    "广告奖励",
    "赚金币",
    "挣金币",
    "金币奖励",
    "视频奖励",
    "看视频赚金币",
    "观看视频获得金币",
    // 部分匹配
    "金币",
    "奖励",
    "免费",
    "获得",
    "领取",
    "观看",
    "播放",
    "视频"
];

// 关闭按钮关键词
var CLOSE_KEYWORDS = [
    "关闭",
    "跳过",
    "×",
    "X",
    "确定",
    "完成",
    "知道了",
    "好的",
    "继续",
    "返回"
];

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_vscode_log.txt";

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    toast(message);
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            return false;
        }
        log("权限检查通过");
        return true;
    } catch (e) {
        log("权限检查失败: " + e.message);
        return false;
    }
}

// 增强的界面分析
function analyzeScreen() {
    log("=== 分析当前界面 ===");
    
    var foundTexts = [];
    var foundButtons = [];
    
    try {
        // 方法1: 分析所有TextView
        var textViews = className("android.widget.TextView").find();
        log("找到TextView数量: " + textViews.length);
        
        for (var i = 0; i < Math.min(textViews.length, 20); i++) {
            var textView = textViews[i];
            var text = textView.text();
            if (text && text.trim().length > 0) {
                foundTexts.push(text);
                log("文本: " + text);
            }
        }
        
        // 方法2: 分析所有Button
        var buttons = className("android.widget.Button").find();
        log("找到Button数量: " + buttons.length);
        
        for (var j = 0; j < buttons.length; j++) {
            var button = buttons[j];
            var btnText = button.text() || button.desc() || "无文本";
            foundButtons.push(btnText);
            log("按钮: " + btnText + " (可点击: " + button.clickable() + ")");
        }
        
        // 方法3: 分析所有可点击元素
        var clickables = clickable(true).find();
        log("找到可点击元素数量: " + clickables.length);
        
        for (var k = 0; k < Math.min(clickables.length, 15); k++) {
            var clickable = clickables[k];
            var clickText = clickable.text() || clickable.desc() || clickable.className();
            log("可点击: " + clickText);
        }
        
        // 方法4: 查找WebView
        var webViews = className("android.webkit.WebView").find();
        if (webViews.length > 0) {
            log("检测到WebView: " + webViews.length + "个 (小程序环境)");
        }
        
    } catch (e) {
        log("界面分析出错: " + e.message);
    }
    
    log("=== 界面分析完成 ===");
    return {
        texts: foundTexts,
        buttons: foundButtons
    };
}

// 智能查找广告按钮
function findAdButton() {
    log("智能查找广告按钮...");
    
    try {
        // 方法1: 精确文本匹配
        for (var i = 0; i < AD_KEYWORDS.length; i++) {
            var keyword = AD_KEYWORDS[i];
            
            // 尝试text()查找
            var element = text(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(精确文本): " + keyword);
                return element;
            }
            
            // 尝试desc()查找
            element = desc(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到广告按钮(精确描述): " + keyword);
                return element;
            }
        }
        
        // 方法2: 包含文本匹配
        for (var j = 0; j < AD_KEYWORDS.length; j++) {
            var keyword2 = AD_KEYWORDS[j];
            
            // 尝试textContains()查找
            var element2 = textContains(keyword2).findOne(1000);
            if (element2 && element2.clickable()) {
                log("找到广告按钮(包含文本): " + keyword2 + " -> " + element2.text());
                return element2;
            }
            
            // 尝试descContains()查找
            element2 = descContains(keyword2).findOne(1000);
            if (element2 && element2.clickable()) {
                log("找到广告按钮(包含描述): " + keyword2 + " -> " + element2.desc());
                return element2;
            }
        }
        
        // 方法3: 通过className查找可能的按钮
        var allButtons = className("android.widget.Button").find();
        for (var k = 0; k < allButtons.length; k++) {
            var btn = allButtons[k];
            var btnText = btn.text() || btn.desc() || "";
            
            // 检查按钮文本是否包含广告相关关键词
            for (var m = 0; m < AD_KEYWORDS.length; m++) {
                if (btnText.indexOf(AD_KEYWORDS[m]) !== -1) {
                    log("找到广告按钮(按钮类名): " + btnText);
                    return btn;
                }
            }
        }
        
        // 方法4: 查找所有可点击元素中包含关键词的
        var allClickables = clickable(true).find();
        for (var n = 0; n < allClickables.length; n++) {
            var clickableEl = allClickables[n];
            var clickText = clickableEl.text() || clickableEl.desc() || "";
            
            // 检查是否包含广告关键词
            if (clickText.indexOf("广告") !== -1 || 
                clickText.indexOf("金币") !== -1 || 
                clickText.indexOf("观看") !== -1 ||
                clickText.indexOf("视频") !== -1) {
                log("找到广告按钮(可点击元素): " + clickText);
                return clickableEl;
            }
        }
        
        log("未找到广告按钮");
        return null;
        
    } catch (e) {
        log("查找广告按钮出错: " + e.message);
        return null;
    }
}

// 智能查找关闭按钮
function findCloseButton() {
    log("查找关闭按钮...");
    
    try {
        for (var i = 0; i < CLOSE_KEYWORDS.length; i++) {
            var keyword = CLOSE_KEYWORDS[i];
            
            // 精确匹配
            var element = text(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到关闭按钮: " + keyword);
                return element;
            }
            
            // 包含匹配
            element = textContains(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("找到关闭按钮(包含): " + keyword);
                return element;
            }
        }
        
        log("未找到关闭按钮");
        return null;
        
    } catch (e) {
        log("查找关闭按钮出错: " + e.message);
        return null;
    }
}

// 安全点击
function safeClick(element, description) {
    if (!element) {
        log("元素不存在: " + description);
        return false;
    }
    
    try {
        if (!element.clickable()) {
            log("元素不可点击: " + description);
            return false;
        }
        
        element.click();
        log("点击成功: " + description);
        return true;
    } catch (e) {
        log("点击失败: " + description + " - " + e.message);
        return false;
    }
}

// 处理广告流程
function handleAdProcess() {
    log("开始处理广告流程...");
    
    try {
        // 1. 查找广告按钮
        var adBtn = findAdButton();
        if (!adBtn) {
            log("未找到广告按钮，跳过");
            return false;
        }
        
        // 2. 点击广告按钮
        if (!safeClick(adBtn, "广告按钮")) {
            log("点击广告按钮失败");
            return false;
        }
        
        adCount++;
        log("第" + adCount + "次广告开始");
        
        // 3. 等待广告加载
        sleep(3000);
        
        // 4. 等待广告播放
        log("等待广告播放...");
        sleep(15000);
        
        // 5. 尝试关闭广告
        var closeBtn = findCloseButton();
        if (closeBtn) {
            if (safeClick(closeBtn, "关闭按钮")) {
                sleep(2000);
                successCount++;
                log("第" + adCount + "次广告完成");
                return true;
            }
        } else {
            log("未找到关闭按钮，尝试返回");
            back();
            sleep(2000);
        }
        
        return true;
        
    } catch (e) {
        log("处理广告流程出错: " + e.message);
        return false;
    }
}

// 主循环
function mainLoop() {
    log("=== 开始VSCode优化版循环 ===");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 分析当前界面
            var screenInfo = analyzeScreen();
            
            // 处理广告
            if (handleAdProcess()) {
                log("广告处理成功");
            } else {
                log("广告处理失败");
                errorCount++;
            }
            
            // 检查错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止脚本");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("循环出错: " + e.message);
            
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止脚本");
                break;
            }
            
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    
    log("=== VSCode版脚本执行完成 ===");
    log("总运行时间: " + totalTime + "秒");
    log("总广告次数: " + adCount + "次");
    log("成功次数: " + successCount + "次");
    log("失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("成功率: " + successRate + "%");
    }
}

// 主程序
function main() {
    log("=== 抖音去水印小程序广告脚本 - VSCode优化版 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("屏幕分辨率: " + device.width + "x" + device.height);
        log("Android版本: " + device.release);
        
        // 使用说明
        log("=== VSCode版使用说明 ===");
        log("1. 确保已在抖音去水印小程序界面");
        log("2. 脚本将智能分析界面元素");
        log("3. 自动查找并点击广告按钮");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 针对VSCode连接环境优化");
        
        // 延迟启动
        sleep(3000);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
    } catch (e) {
        log("主程序出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
}

// 启动脚本
console.log("准备启动VSCode优化版脚本...");
main();
