// ========================================
// 广告按钮调试脚本 - 专门分析为什么找不到广告按钮
// 针对VSCode连接环境和小程序界面
// 版本：v1.0 (调试专用)
// ========================================

console.log("广告按钮调试脚本启动...");

// 日志文件路径
var LOG_FILE = "/sdcard/debug_ad_button_log.txt";

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    toast(message);
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 无障碍服务未开启");
            return false;
        }
        log("✅ 无障碍服务已开启");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message);
        return false;
    }
}

// 全面分析当前界面
function fullScreenAnalysis() {
    log("=== 🔍 开始全面界面分析 ===");
    
    try {
        // 1. 基本信息
        log("📱 设备信息:");
        log("  品牌: " + device.brand);
        log("  型号: " + device.model);
        log("  分辨率: " + device.width + "x" + device.height);
        log("  Android版本: " + device.release);
        
        // 2. 当前应用信息
        try {
            var currentPkg = currentPackage();
            log("📦 当前应用包名: " + currentPkg);
            
            if (currentPkg === "com.tencent.mm") {
                log("✅ 当前在微信中");
            } else {
                log("⚠️ 当前不在微信中，可能影响小程序检测");
            }
        } catch (e) {
            log("❌ 获取当前应用失败: " + e.message);
        }
        
        // 3. 分析所有TextView
        log("📝 分析TextView元素:");
        var textViews = className("android.widget.TextView").find();
        log("  找到TextView数量: " + textViews.length);
        
        var textCount = 0;
        for (var i = 0; i < Math.min(textViews.length, 30); i++) {
            var textView = textViews[i];
            var text = textView.text();
            if (text && text.trim().length > 0) {
                textCount++;
                var isClickable = textView.clickable() ? "可点击" : "不可点击";
                log("  文本" + textCount + ": \"" + text + "\" (" + isClickable + ")");
                
                // 检查是否包含广告相关关键词
                if (text.indexOf("广告") !== -1 || 
                    text.indexOf("金币") !== -1 || 
                    text.indexOf("观看") !== -1 ||
                    text.indexOf("视频") !== -1 ||
                    text.indexOf("奖励") !== -1) {
                    log("  🎯 发现广告相关文本: \"" + text + "\"");
                }
            }
        }
        
        // 4. 分析所有Button
        log("🔘 分析Button元素:");
        var buttons = className("android.widget.Button").find();
        log("  找到Button数量: " + buttons.length);
        
        for (var j = 0; j < buttons.length; j++) {
            var button = buttons[j];
            var btnText = button.text() || "无文本";
            var btnDesc = button.desc() || "无描述";
            var isClickable = button.clickable() ? "可点击" : "不可点击";
            log("  按钮" + (j+1) + ": 文本=\"" + btnText + "\", 描述=\"" + btnDesc + "\" (" + isClickable + ")");
        }
        
        // 5. 分析所有可点击元素
        log("👆 分析可点击元素:");
        var clickables = clickable(true).find();
        log("  找到可点击元素数量: " + clickables.length);
        
        for (var k = 0; k < Math.min(clickables.length, 20); k++) {
            var clickable = clickables[k];
            var clickText = clickable.text() || "无文本";
            var clickDesc = clickable.desc() || "无描述";
            var className = clickable.className() || "无类名";
            log("  可点击" + (k+1) + ": \"" + clickText + "\" / \"" + clickDesc + "\" [" + className + "]");
            
            // 检查是否是潜在的广告按钮
            if ((clickText.indexOf("广告") !== -1 || clickText.indexOf("金币") !== -1 || 
                 clickText.indexOf("观看") !== -1 || clickText.indexOf("视频") !== -1) ||
                (clickDesc.indexOf("广告") !== -1 || clickDesc.indexOf("金币") !== -1 || 
                 clickDesc.indexOf("观看") !== -1 || clickDesc.indexOf("视频") !== -1)) {
                log("  🎯 发现潜在广告按钮: \"" + clickText + "\" / \"" + clickDesc + "\"");
            }
        }
        
        // 6. 检查WebView
        log("🌐 检查WebView:");
        var webViews = className("android.webkit.WebView").find();
        log("  找到WebView数量: " + webViews.length);
        
        if (webViews.length > 0) {
            log("  ✅ 检测到WebView，这是小程序环境的标志");
            for (var m = 0; m < webViews.length; m++) {
                var webView = webViews[m];
                log("  WebView" + (m+1) + " 包名: " + webView.packageName());
            }
        } else {
            log("  ⚠️ 未检测到WebView，可能不在小程序中");
        }
        
        // 7. 检查特定的小程序元素
        log("🔍 检查小程序特征:");
        var miniProgramKeywords = [
            "抖音", "去水印", "下载", "解析", "小程序", "微信"
        ];
        
        for (var n = 0; n < miniProgramKeywords.length; n++) {
            var keyword = miniProgramKeywords[n];
            var found = textContains(keyword).find();
            if (found.length > 0) {
                log("  ✅ 找到小程序特征 \"" + keyword + "\": " + found.length + "个");
            }
        }
        
    } catch (e) {
        log("❌ 界面分析出错: " + e.message);
    }
    
    log("=== 🔍 界面分析完成 ===");
}

// 专门测试广告按钮查找
function testAdButtonSearch() {
    log("=== 🎯 测试广告按钮查找 ===");
    
    var adKeywords = [
        "观看广告获取金币", "观看广告", "看广告", "广告", 
        "观看视频", "看视频", "视频广告", "激励视频",
        "获取奖励", "领取奖励", "获得金币", "赚取金币",
        "免费领取", "免费获得", "金币", "奖励", "视频"
    ];
    
    var foundButtons = [];
    
    for (var i = 0; i < adKeywords.length; i++) {
        var keyword = adKeywords[i];
        
        try {
            // 测试精确文本匹配
            var exactText = text(keyword).findOne(1000);
            if (exactText) {
                var info = {
                    method: "精确文本",
                    keyword: keyword,
                    text: exactText.text(),
                    clickable: exactText.clickable(),
                    element: exactText
                };
                foundButtons.push(info);
                log("  ✅ " + info.method + " 找到: \"" + keyword + "\" -> \"" + info.text + "\" (可点击: " + info.clickable + ")");
            }
            
            // 测试包含文本匹配
            var containsText = textContains(keyword).findOne(1000);
            if (containsText && containsText !== exactText) {
                var info2 = {
                    method: "包含文本",
                    keyword: keyword,
                    text: containsText.text(),
                    clickable: containsText.clickable(),
                    element: containsText
                };
                foundButtons.push(info2);
                log("  ✅ " + info2.method + " 找到: \"" + keyword + "\" -> \"" + info2.text + "\" (可点击: " + info2.clickable + ")");
            }
            
            // 测试描述匹配
            var exactDesc = desc(keyword).findOne(1000);
            if (exactDesc) {
                var info3 = {
                    method: "精确描述",
                    keyword: keyword,
                    desc: exactDesc.desc(),
                    clickable: exactDesc.clickable(),
                    element: exactDesc
                };
                foundButtons.push(info3);
                log("  ✅ " + info3.method + " 找到: \"" + keyword + "\" -> \"" + info3.desc + "\" (可点击: " + info3.clickable + ")");
            }
            
        } catch (e) {
            log("  ❌ 测试关键词 \"" + keyword + "\" 失败: " + e.message);
        }
    }
    
    log("📊 广告按钮查找结果:");
    log("  总共找到 " + foundButtons.length + " 个潜在广告按钮");
    
    if (foundButtons.length === 0) {
        log("  ❌ 未找到任何广告按钮！");
        log("  💡 可能的原因:");
        log("    1. 当前不在小程序界面");
        log("    2. 小程序界面结构发生变化");
        log("    3. 广告按钮使用了不同的文本");
        log("    4. 按钮可能是图片或自定义控件");
        log("    5. VSCode连接影响了元素检测");
    } else {
        log("  ✅ 找到潜在广告按钮，建议测试点击:");
        for (var j = 0; j < foundButtons.length; j++) {
            var btn = foundButtons[j];
            log("    " + (j+1) + ". " + btn.method + ": \"" + (btn.text || btn.desc) + "\" (可点击: " + btn.clickable + ")");
        }
    }
    
    log("=== 🎯 广告按钮查找测试完成 ===");
    return foundButtons;
}

// 测试点击功能
function testClickFunction(buttons) {
    if (buttons.length === 0) {
        log("⚠️ 没有可测试的按钮");
        return;
    }
    
    log("=== 🖱️ 测试点击功能 ===");
    
    // 只测试第一个可点击的按钮
    for (var i = 0; i < buttons.length; i++) {
        var btn = buttons[i];
        if (btn.clickable) {
            log("🧪 测试点击: \"" + (btn.text || btn.desc) + "\"");
            
            try {
                // 这里只是模拟，不实际点击
                log("  📍 按钮位置: " + btn.element.bounds().centerX() + ", " + btn.element.bounds().centerY());
                log("  ✅ 按钮可以点击，建议手动验证");
                
                // 如果用户确认要测试点击，可以取消下面的注释
                // btn.element.click();
                // log("  ✅ 点击成功");
                
                break;
            } catch (e) {
                log("  ❌ 点击测试失败: " + e.message);
            }
        }
    }
    
    log("=== 🖱️ 点击功能测试完成 ===");
}

// 主程序
function main() {
    log("=== 🚀 广告按钮调试脚本启动 ===");
    
    try {
        // 1. 检查权限
        if (!checkPermissions()) {
            log("❌ 权限检查失败，请先开启无障碍服务");
            return;
        }
        
        // 2. 全面分析界面
        fullScreenAnalysis();
        
        // 3. 测试广告按钮查找
        var foundButtons = testAdButtonSearch();
        
        // 4. 测试点击功能
        testClickFunction(foundButtons);
        
        log("=== 📋 调试建议 ===");
        log("1. 确保在抖音去水印小程序的主界面");
        log("2. 检查小程序是否有金币系统");
        log("3. 查看是否有\"观看广告\"相关按钮");
        log("4. 如果使用VSCode连接，尝试断开后重试");
        log("5. 查看日志文件: " + LOG_FILE);
        
        log("=== ✅ 调试完成 ===");
        
    } catch (e) {
        log("❌ 调试脚本出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
}

// 启动脚本
console.log("准备启动广告按钮调试脚本...");
main();
