// ========================================
// 抖音去水印小程序广告自动刷脚本 - 简化测试版
// 解决Auto.js无响应问题
// 版本：v3.2 (简化版)
// ========================================

console.log("脚本开始启动...");

// 基础检查
try {
    console.log("检查Auto.js基础功能...");
    
    // 检查关键API
    if (typeof text === 'undefined') {
        throw new Error("text API未定义");
    }
    if (typeof desc === 'undefined') {
        throw new Error("desc API未定义");
    }
    if (typeof id === 'undefined') {
        throw new Error("id API未定义");
    }
    
    console.log("API检查通过");
} catch (e) {
    console.log("API检查失败: " + e.message);
    toast("Auto.js API不可用");
    exit();
}

// 简化配置
var CONFIG = {
    interval: 15,              // 每次操作间隔15秒
    adWaitTime: 30000,         // 广告等待时间30秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 5000,         // 等待超时时间5秒
    testMode: true             // 测试模式
};

// 简化的界面元素识别
var UI_SELECTORS = {
    adButtons: [
        "观看广告获取金币",
        "观看广告",
        "看广告",
        "广告",
        "观看视频",
        "看视频"
    ],
    
    closeButtons: [
        "关闭",
        "跳过",
        "×",
        "X",
        "确定"
    ],
    
    confirmButtons: [
        "确定",
        "确认",
        "知道了",
        "好的",
        "完成"
    ]
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_ad_simple_log.txt";

// 简化的日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    // 使用较短的toast时间
    toast(message);
}

// 简化的权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            toast("请开启无障碍服务");
            return false;
        }
        
        log("权限检查通过");
        return true;
    } catch (e) {
        log("权限检查失败: " + e.message);
        return false;
    }
}

// 简化的元素查找
function findElement(selectors, timeout, description) {
    timeout = timeout || CONFIG.waitTimeout;
    
    try {
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            var element = text(selector).findOne(timeout);
            
            if (element) {
                log("找到元素: " + selector);
                return element;
            }
        }
    } catch (e) {
        log("查找元素出错: " + e.message);
    }
    
    return null;
}

// 简化的点击函数
function simpleClick(element, description) {
    if (!element) {
        log("元素不存在: " + description);
        return false;
    }
    
    try {
        element.click();
        log("点击成功: " + description);
        return true;
    } catch (e) {
        log("点击失败: " + description + " - " + e.message);
        return false;
    }
}

// 检查当前应用
function checkCurrentApp() {
    try {
        var currentPkg = currentPackage();
        log("当前应用: " + currentPkg);
        
        if (currentPkg !== "com.tencent.mm") {
            log("当前不在微信中");
            return false;
        }
        
        return true;
    } catch (e) {
        log("检查当前应用失败: " + e.message);
        return false;
    }
}

// 简化的广告处理
function handleAd() {
    log("开始处理广告...");
    
    try {
        // 查找广告按钮
        var adBtn = findElement(UI_SELECTORS.adButtons, CONFIG.waitTimeout, "广告按钮");
        
        if (adBtn) {
            if (simpleClick(adBtn, "广告按钮")) {
                adCount++;
                log("第" + adCount + "次广告开始");
                
                // 等待一段时间
                sleep(5000);
                
                // 尝试关闭广告
                var closeBtn = findElement(UI_SELECTORS.closeButtons, 3000, "关闭按钮");
                if (closeBtn) {
                    simpleClick(closeBtn, "关闭按钮");
                    sleep(1000);
                    
                    // 确认按钮
                    var confirmBtn = findElement(UI_SELECTORS.confirmButtons, 2000, "确认按钮");
                    if (confirmBtn) {
                        simpleClick(confirmBtn, "确认按钮");
                    }
                    
                    successCount++;
                    log("第" + adCount + "次广告完成");
                    return true;
                }
            }
        } else {
            log("未找到广告按钮");
        }
        
        return false;
    } catch (e) {
        log("处理广告出错: " + e.message);
        return false;
    }
}

// 简化的主循环
function simpleLoop() {
    log("=== 开始简化测试循环 ===");
    
    var maxLoops = 3; // 最多循环3次
    
    for (var i = 0; i < maxLoops; i++) {
        try {
            log("测试循环 " + (i + 1) + "/" + maxLoops);
            
            // 检查应用状态
            if (!checkCurrentApp()) {
                log("应用状态检查失败");
                sleep(3000);
                continue;
            }
            
            // 处理广告
            if (handleAd()) {
                log("广告处理成功");
            } else {
                log("广告处理失败");
                errorCount++;
            }
            
            // 等待间隔
            log("等待" + CONFIG.interval + "秒...");
            sleep(CONFIG.interval * 1000);
            
        } catch (e) {
            errorCount++;
            log("循环出错: " + e.message);
            
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止测试");
                break;
            }
            
            sleep(3000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    
    log("=== 测试完成 ===");
    log("总运行时间: " + totalTime + "秒");
    log("总广告次数: " + adCount + "次");
    log("成功次数: " + successCount + "次");
    log("失败次数: " + errorCount + "次");
}

// 主程序
function main() {
    log("=== 抖音去水印小程序广告脚本 - 简化测试版 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("屏幕分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 使用说明 ===");
        log("1. 请先打开微信中的抖音去水印小程序");
        log("2. 确保小程序界面显示正常");
        log("3. 脚本将进行简化测试");
        
        // 延迟启动
        sleep(3000);
        
        // 开始测试循环
        simpleLoop();
        
        // 显示结果
        showResults();
        
    } catch (e) {
        log("主程序出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
    
    log("脚本执行完成");
}

// 启动脚本
console.log("准备启动主程序...");
main();
