# 抖音去水印小程序广告自动刷脚本 - 使用说明

## 问题修复说明

### 原始问题
- **错误信息**: `org.mozilla.javascript.Undefined@0 is not a function, it is undefined`
- **根本原因**: 第399行变量名冲突 `var currentPackage = currentPackage();`
- **影响**: 脚本启动后立即失败，无法正常运行

### 修复内容
1. ✅ **修复变量名冲突**: 将 `var currentPackage = currentPackage();` 改为 `var currentPkg = currentPackage();`
2. ✅ **增强API检查**: 添加Auto.js API兼容性检查
3. ✅ **改进错误处理**: 为所有关键函数添加try-catch错误处理
4. ✅ **优化权限检查**: 增强无障碍服务和屏幕录制权限检查
5. ✅ **完善日志系统**: 添加更详细的调试信息

## 文件说明

### 主要文件
- `auto_ad_fix.js` - 修复版脚本（推荐使用）
- `抖音去水印小程序广告自动刷脚本.js` - 原始脚本（已修复部分问题）
- `logs/autojs_ad_log.txt` - 运行日志文件

### 脚本功能
- 自动检测抖音去水印小程序界面
- 智能识别广告按钮并点击
- 自动等待广告播放完成
- 金币状态监控和管理
- 华为设备优化支持
- 详细的运行统计

## 使用步骤

### 1. 环境准备
- **Auto.js版本**: 建议使用4.1.1或更高版本
- **Android版本**: 支持Android 5.0+
- **设备**: 支持所有Android设备，华为设备有特殊优化

### 2. 权限设置
1. 开启Auto.js无障碍服务
2. 授予屏幕录制权限
3. 允许Auto.js悬浮窗权限

### 3. 使用流程
1. 打开微信，进入抖音去水印小程序
2. 确保小程序界面正常显示
3. 运行 `auto_ad_fix.js` 脚本
4. 脚本会自动检测并开始刷广告

### 4. 脚本配置
```javascript
var CONFIG = {
    totalTime: 60 * 60,        // 总运行时长60分钟
    interval: 10,              // 每次操作间隔10秒
    adWaitTime: 35000,         // 广告等待时间35秒
    maxRetries: 5,             // 最大重试次数
    waitTimeout: 8000,         // 等待超时时间
    huaweiOptimization: true,  // 华为优化开关
    debugMode: true            // 调试模式
};
```

## 故障排除

### 常见问题

#### 1. 脚本无法启动
- **检查**: Auto.js服务是否正常运行
- **解决**: 重启Auto.js应用，确保无障碍服务已开启

#### 2. 找不到广告按钮
- **检查**: 是否在正确的小程序界面
- **解决**: 确保在抖音去水印小程序中，界面显示正常

#### 3. 权限问题
- **检查**: 无障碍服务和屏幕录制权限
- **解决**: 在设置中手动开启相关权限

#### 4. 华为设备特殊问题
- **检查**: 华为优化是否启用
- **解决**: 在CONFIG中设置 `huaweiOptimization: true`

### 日志分析
- 查看 `/sdcard/autojs_ad_log.txt` 文件
- 关注错误信息和执行状态
- 根据日志信息调整配置

## 技术特性

### 智能识别
- 基于小程序源码分析的元素识别
- 多种广告按钮文本匹配
- 金币状态实时监控

### 华为优化
- 针对华为设备的触摸优化
- 内存管理优化
- 屏幕适配优化

### 错误处理
- 完善的异常捕获机制
- 自动重试机制
- 详细的错误日志

## 注意事项

1. **合规使用**: 仅用于个人学习和测试，请遵守相关服务条款
2. **适度使用**: 避免过度频繁操作，建议设置合理的间隔时间
3. **设备安全**: 确保设备电量充足，避免长时间运行导致设备过热
4. **网络环境**: 确保网络连接稳定，避免广告加载失败

## 更新日志

### v3.1 (修复版)
- 修复变量名冲突导致的脚本崩溃问题
- 增强API兼容性检查
- 改进错误处理机制
- 优化华为设备支持
- 完善日志系统

### v3.0 (原版)
- 基于小程序源码分析
- 支持金币系统
- 华为设备优化
- 智能广告识别

## 技术支持

如果遇到问题，请：
1. 查看日志文件 `/sdcard/autojs_ad_log.txt`
2. 检查Auto.js版本兼容性
3. 确认权限设置正确
4. 验证小程序界面状态

---

**免责声明**: 本脚本仅供学习和研究使用，使用者需自行承担使用风险，开发者不承担任何责任。
