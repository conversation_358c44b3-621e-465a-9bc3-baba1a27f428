// ========================================
// 抖音去水印小程序广告自动刷脚本 - 改进版
// 解决应用检测和界面识别问题
// 版本：v3.3 (改进版)
// ========================================

console.log("脚本开始启动...");

// 基础检查
try {
    console.log("检查Auto.js基础功能...");
    
    if (typeof text === 'undefined' || typeof desc === 'undefined' || typeof id === 'undefined') {
        throw new Error("关键API未定义");
    }
    
    console.log("API检查通过");
} catch (e) {
    console.log("API检查失败: " + e.message);
    toast("Auto.js API不可用");
    exit();
}

// 配置参数
var CONFIG = {
    interval: 15,              // 每次操作间隔15秒
    adWaitTime: 30000,         // 广告等待时间30秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 3000,         // 等待超时时间3秒
    maxLoops: 5                // 最大循环次数
};

// 界面元素识别
var UI_SELECTORS = {
    // 小程序标识文本
    miniProgramIndicators: [
        "抖音去水印",
        "抖音视频",
        "无水印下载",
        "视频解析",
        "一键解析",
        "快速提取",
        "原始高清视频"
    ],
    
    // 广告相关按钮
    adButtons: [
        "观看广告获取金币",
        "观看广告",
        "看广告",
        "广告",
        "观看视频",
        "看视频",
        "视频广告",
        "激励视频",
        "获取奖励",
        "领取奖励",
        "获得金币",
        "赚取金币"
    ],
    
    // 金币相关文本
    coinTexts: [
        "金币",
        "💰",
        "/5000",
        "0/",
        "1/",
        "2/",
        "3/",
        "4/",
        "5/"
    ],
    
    // 关闭按钮
    closeButtons: [
        "关闭",
        "跳过",
        "×",
        "X",
        "确定",
        "完成"
    ]
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_ad_improved_log.txt";

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    toast(message);
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            toast("请开启无障碍服务");
            return false;
        }
        
        log("权限检查通过");
        return true;
    } catch (e) {
        log("权限检查失败: " + e.message);
        return false;
    }
}

// 改进的元素查找 - 支持多种查找方式
function findElement(selectors, timeout, description) {
    timeout = timeout || CONFIG.waitTimeout;
    
    try {
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            
            // 尝试文本查找
            var element = text(selector).findOne(timeout);
            if (element) {
                log("找到元素(文本): " + selector);
                return element;
            }
            
            // 尝试包含文本查找
            element = textContains(selector).findOne(timeout);
            if (element) {
                log("找到元素(包含): " + selector);
                return element;
            }
            
            // 尝试描述查找
            element = desc(selector).findOne(timeout);
            if (element) {
                log("找到元素(描述): " + selector);
                return element;
            }
            
            // 尝试包含描述查找
            element = descContains(selector).findOne(timeout);
            if (element) {
                log("找到元素(包含描述): " + selector);
                return element;
            }
        }
    } catch (e) {
        log("查找元素出错: " + e.message);
    }
    
    return null;
}

// 安全点击函数
function safeClick(element, description) {
    if (!element) {
        log("元素不存在: " + description);
        return false;
    }
    
    try {
        // 检查元素是否可点击
        if (!element.clickable()) {
            log("元素不可点击: " + description);
            return false;
        }
        
        element.click();
        log("点击成功: " + description);
        return true;
    } catch (e) {
        log("点击失败: " + description + " - " + e.message);
        return false;
    }
}

// 改进的小程序检测
function checkMiniProgram() {
    try {
        log("检查小程序界面...");
        
        // 获取当前界面的所有文本
        var allTexts = [];
        try {
            var textNodes = textContains("").find();
            for (var i = 0; i < textNodes.length && i < 20; i++) {
                var nodeText = textNodes[i].text();
                if (nodeText && nodeText.length > 0) {
                    allTexts.push(nodeText);
                }
            }
        } catch (e) {
            log("获取界面文本失败: " + e.message);
        }
        
        log("界面文本示例: " + allTexts.slice(0, 5).join(", "));
        
        // 检查是否包含小程序标识
        var foundIndicator = findElement(UI_SELECTORS.miniProgramIndicators, 2000, "小程序标识");
        if (foundIndicator) {
            log("检测到抖音去水印小程序界面");
            return true;
        }
        
        // 检查是否包含金币相关文本
        var foundCoin = findElement(UI_SELECTORS.coinTexts, 2000, "金币文本");
        if (foundCoin) {
            log("检测到金币系统，可能是目标小程序");
            return true;
        }
        
        log("未检测到小程序特征");
        return false;
        
    } catch (e) {
        log("检查小程序失败: " + e.message);
        return false;
    }
}

// 查找并处理广告
function handleAdvertisement() {
    log("查找广告按钮...");
    
    try {
        // 查找广告按钮
        var adBtn = findElement(UI_SELECTORS.adButtons, CONFIG.waitTimeout, "广告按钮");
        
        if (adBtn) {
            log("找到广告按钮: " + adBtn.text());
            
            if (safeClick(adBtn, "广告按钮")) {
                adCount++;
                log("第" + adCount + "次广告开始");
                
                // 等待广告加载
                sleep(3000);
                
                // 等待一段时间模拟观看广告
                log("等待广告播放...");
                sleep(10000);
                
                // 尝试关闭广告
                var closeBtn = findElement(UI_SELECTORS.closeButtons, 5000, "关闭按钮");
                if (closeBtn) {
                    if (safeClick(closeBtn, "关闭按钮")) {
                        sleep(2000);
                        successCount++;
                        log("第" + adCount + "次广告完成");
                        return true;
                    }
                } else {
                    log("未找到关闭按钮，尝试返回");
                    back();
                    sleep(2000);
                }
                
                return true;
            }
        } else {
            log("未找到广告按钮");
        }
        
        return false;
    } catch (e) {
        log("处理广告出错: " + e.message);
        return false;
    }
}

// 主循环
function mainLoop() {
    log("=== 开始改进版测试循环 ===");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("测试循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 检查小程序状态
            if (!checkMiniProgram()) {
                log("小程序界面检查失败，等待...");
                sleep(5000);
                continue;
            }
            
            // 处理广告
            if (handleAdvertisement()) {
                log("广告处理成功");
            } else {
                log("广告处理失败");
                errorCount++;
            }
            
            // 检查错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止测试");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("循环出错: " + e.message);
            
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止测试");
                break;
            }
            
            sleep(3000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    
    log("=== 测试完成 ===");
    log("总运行时间: " + totalTime + "秒");
    log("总广告次数: " + adCount + "次");
    log("成功次数: " + successCount + "次");
    log("失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("成功率: " + successRate + "%");
    }
}

// 主程序
function main() {
    log("=== 抖音去水印小程序广告脚本 - 改进版 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("屏幕分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 使用说明 ===");
        log("1. 请确保已在抖音去水印小程序界面");
        log("2. 脚本将自动检测界面特征");
        log("3. 找到广告按钮后自动点击");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        
        // 延迟启动
        sleep(3000);
        
        // 开始测试循环
        mainLoop();
        
        // 显示结果
        showResults();
        
    } catch (e) {
        log("主程序出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
    
    log("脚本执行完成");
}

// 启动脚本
console.log("准备启动改进版脚本...");
main();
