// ========================================
// 抖音去水印小程序广告自动刷脚本 - 修正调试版
// 修复API调用问题
// 版本：v3.5 (修正调试版)
// ========================================

console.log("修正调试脚本启动...");

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    toast(message);
    
    try {
        files.append("/sdcard/autojs_debug_fixed_log.txt", logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
}

// 权限检查
function checkPermissions() {
    log("检查权限...");
    
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            return false;
        }
        log("权限检查通过");
        return true;
    } catch (e) {
        log("权限检查失败: " + e.message);
        return false;
    }
}

// 分析当前界面 - 修正版
function analyzeCurrentScreen() {
    log("=== 开始分析当前界面 ===");
    
    try {
        // 方法1: 使用正确的API获取所有可见文本
        log("方法1: 获取界面中的所有文本");
        try {
            // 获取所有包含文本的节点
            var allNodes = className("android.widget.TextView").find();
            log("找到TextView节点数量: " + allNodes.length);
            
            var textCount = 0;
            for (var i = 0; i < Math.min(allNodes.length, 15); i++) {
                var node = allNodes[i];
                var nodeText = node.text();
                if (nodeText && nodeText.trim().length > 0) {
                    textCount++;
                    log("文本" + textCount + ": " + nodeText);
                }
            }
            
            if (textCount === 0) {
                log("未找到任何文本内容");
            }
        } catch (e) {
            log("方法1失败: " + e.message);
        }
        
        // 方法2: 查找Button类型的元素
        log("方法2: 查找按钮元素");
        try {
            var buttonNodes = className("android.widget.Button").find();
            log("找到Button节点数量: " + buttonNodes.length);
            
            for (var j = 0; j < Math.min(buttonNodes.length, 5); j++) {
                var btnNode = buttonNodes[j];
                var btnText = btnNode.text() || btnNode.desc() || "无文本";
                log("按钮" + (j+1) + ": " + btnText + " (可点击: " + btnNode.clickable() + ")");
            }
        } catch (e) {
            log("方法2失败: " + e.message);
        }
        
        // 方法3: 查找ImageView（可能包含图标）
        log("方法3: 查找图像元素");
        try {
            var imageNodes = className("android.widget.ImageView").find();
            log("找到ImageView节点数量: " + imageNodes.length);
            
            for (var k = 0; k < Math.min(imageNodes.length, 5); k++) {
                var imgNode = imageNodes[k];
                var imgDesc = imgNode.desc() || "无描述";
                log("图像" + (k+1) + ": " + imgDesc + " (可点击: " + imgNode.clickable() + ")");
            }
        } catch (e) {
            log("方法3失败: " + e.message);
        }
        
        // 方法4: 查找所有可点击元素
        log("方法4: 查找所有可点击元素");
        try {
            var clickableNodes = clickable(true).find();
            log("找到可点击元素数量: " + clickableNodes.length);
            
            for (var m = 0; m < Math.min(clickableNodes.length, 8); m++) {
                var clickNode = clickableNodes[m];
                var clickText = clickNode.text() || clickNode.desc() || clickNode.className();
                log("可点击" + (m+1) + ": " + clickText);
            }
        } catch (e) {
            log("方法4失败: " + e.message);
        }
        
        // 方法5: 查找WebView（小程序通常运行在WebView中）
        log("方法5: 查找WebView元素");
        try {
            var webViewNodes = className("android.webkit.WebView").find();
            log("找到WebView节点数量: " + webViewNodes.length);
            
            if (webViewNodes.length > 0) {
                log("检测到WebView，这可能是小程序界面");
                
                // 尝试获取WebView中的内容
                for (var n = 0; n < Math.min(webViewNodes.length, 2); n++) {
                    var webView = webViewNodes[n];
                    log("WebView" + (n+1) + " 包名: " + webView.packageName());
                    
                    // 查找WebView内的子元素
                    try {
                        var webChildren = webView.children();
                        log("WebView" + (n+1) + " 子元素数量: " + webChildren.length);
                    } catch (e) {
                        log("获取WebView子元素失败: " + e.message);
                    }
                }
            }
        } catch (e) {
            log("方法5失败: " + e.message);
        }
        
    } catch (e) {
        log("界面分析出错: " + e.message);
    }
    
    log("=== 界面分析完成 ===");
}

// 查找特定关键词
function searchKeywords() {
    log("=== 搜索关键词 ===");
    
    var keywords = [
        "抖音",
        "去水印", 
        "广告",
        "观看",
        "视频",
        "金币",
        "奖励",
        "免费",
        "领取",
        "获得",
        "点击",
        "播放"
    ];
    
    for (var i = 0; i < keywords.length; i++) {
        try {
            var keyword = keywords[i];
            
            // 使用textContains查找
            var textNodes = textContains(keyword).find();
            if (textNodes.length > 0) {
                log("找到包含'" + keyword + "'的文本: " + textNodes.length + "个");
                for (var j = 0; j < Math.min(textNodes.length, 2); j++) {
                    log("  - " + textNodes[j].text());
                }
            }
            
            // 使用descContains查找
            var descNodes = descContains(keyword).find();
            if (descNodes.length > 0) {
                log("找到包含'" + keyword + "'的描述: " + descNodes.length + "个");
            }
            
        } catch (e) {
            log("搜索'" + keyword + "'失败: " + e.message);
        }
    }
    
    log("=== 关键词搜索完成 ===");
}

// 尝试屏幕截图分析
function analyzeScreenshot() {
    log("=== 尝试屏幕截图分析 ===");
    
    try {
        // 检查是否有屏幕录制权限
        if (!requestScreenCapture()) {
            log("没有屏幕录制权限，跳过截图分析");
            return;
        }
        
        log("获取屏幕录制权限成功");
        sleep(1000);
        
        // 尝试截图
        var img = captureScreen();
        if (img) {
            log("截图成功，图片尺寸: " + img.width + "x" + img.height);
            
            // 可以在这里添加OCR文字识别，但需要额外的库
            log("截图分析功能需要OCR支持，当前跳过");
            
            img.recycle(); // 释放图片内存
        } else {
            log("截图失败");
        }
        
    } catch (e) {
        log("截图分析失败: " + e.message);
    }
    
    log("=== 截图分析完成 ===");
}

// 主程序
function main() {
    log("=== 抖音去水印小程序界面修正调试脚本 ===");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            log("权限检查失败，退出");
            return;
        }
        
        // 显示设备信息
        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("屏幕分辨率: " + device.width + "x" + device.height);
        log("Android版本: " + device.release);
        
        // 使用说明
        log("=== 调试说明 ===");
        log("1. 请确保已在目标小程序界面");
        log("2. 脚本将使用多种方法分析界面");
        log("3. 查看详细日志了解界面结构");
        
        // 延迟启动
        sleep(3000);
        
        // 分析当前界面
        analyzeCurrentScreen();
        
        // 搜索关键词
        searchKeywords();
        
        // 尝试截图分析
        analyzeScreenshot();
        
        log("=== 调试完成 ===");
        log("请查看日志文件: /sdcard/autojs_debug_fixed_log.txt");
        
    } catch (e) {
        log("调试脚本出错: " + e.message);
        console.log("错误堆栈: " + e.stack);
    }
}

// 启动脚本
console.log("准备启动修正调试脚本...");
main();
