// ========================================
// 抖音去水印小程序广告自动刷脚本 - 图像识别版
// 使用图像识别技术识别广告按钮
// 版本：v9.0 (图像识别版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.9, device.height * 0.6);
console.setPosition(50, 100);

console.log("🚀 图像识别版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 35,              // 每次操作间隔35秒
    adWaitTime: 25000,         // 广告等待时间25秒
    maxRetries: 3,             // 最大重试次数
    maxLoops: 12,              // 最大循环次数
    imageThreshold: 0.8,       // 图像匹配阈值
    colorTolerance: 10,        // 颜色容差
    imageMode: true            // 图像模式
};

// 广告按钮可能的颜色特征
var AD_COLORS = [
    "#FF6B35",  // 橙红色（常见的广告按钮颜色）
    "#FF4444",  // 红色
    "#4CAF50",  // 绿色
    "#2196F3",  // 蓝色
    "#FFC107",  // 黄色
    "#FF9800",  // 橙色
    "#E91E63"   // 粉色
];

// 关闭按钮可能的颜色特征
var CLOSE_COLORS = [
    "#FFFFFF",  // 白色
    "#000000",  // 黑色
    "#666666",  // 灰色
    "#CCCCCC"   // 浅灰色
];

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_image_log.txt";

// 日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;
    
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    if (level === "SUCCESS" || level === "ERROR") {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            return false;
        }
        
        // 检查屏幕录制权限
        if (!requestScreenCapture()) {
            log("❌ 需要屏幕录制权限", "ERROR");
            return false;
        }
        
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 切换到微信
function switchToWechat() {
    log("📱 切换到微信...");
    
    try {
        app.launch("com.tencent.mm");
        sleep(3000);
        
        var currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 成功切换到微信", "SUCCESS");
            return true;
        } else {
            log("❌ 切换失败，当前应用: " + currentPkg, "ERROR");
            return false;
        }
    } catch (e) {
        log("❌ 切换到微信出错: " + e.message, "ERROR");
        return false;
    }
}

// 颜色匹配函数
function isColorMatch(color1, color2, tolerance) {
    tolerance = tolerance || CONFIG.colorTolerance;
    
    var r1 = parseInt(color1.substring(1, 3), 16);
    var g1 = parseInt(color1.substring(3, 5), 16);
    var b1 = parseInt(color1.substring(5, 7), 16);
    
    var r2 = parseInt(color2.substring(1, 3), 16);
    var g2 = parseInt(color2.substring(3, 5), 16);
    var b2 = parseInt(color2.substring(5, 7), 16);
    
    return Math.abs(r1 - r2) <= tolerance && 
           Math.abs(g1 - g2) <= tolerance && 
           Math.abs(b1 - b2) <= tolerance;
}

// 在指定区域查找特定颜色
function findColorInRegion(img, colors, region) {
    try {
        var startX = region.x || 0;
        var startY = region.y || 0;
        var width = region.width || img.width;
        var height = region.height || img.height;
        
        var endX = Math.min(startX + width, img.width);
        var endY = Math.min(startY + height, img.height);
        
        for (var y = startY; y < endY; y += 5) { // 每5像素检查一次，提高效率
            for (var x = startX; x < endX; x += 5) {
                var pixel = images.pixel(img, x, y);
                var hexColor = "#" + colors.toString(pixel, 16).padStart(6, '0');
                
                for (var i = 0; i < colors.length; i++) {
                    if (isColorMatch(hexColor, colors[i])) {
                        return {x: x, y: y, color: hexColor};
                    }
                }
            }
        }
        
        return null;
    } catch (e) {
        log("❌ 颜色查找出错: " + e.message, "ERROR");
        return null;
    }
}

// 查找广告按钮（基于图像分析）
function findAdButtonByImage() {
    log("🔍 使用图像识别查找广告按钮...");
    
    try {
        var img = captureScreen();
        if (!img) {
            log("❌ 截图失败", "ERROR");
            return null;
        }
        
        log("📸 截图成功: " + img.width + "x" + img.height);
        
        // 定义搜索区域（屏幕下半部分，广告按钮通常在这里）
        var searchRegions = [
            // 屏幕下方区域
            {
                x: 0,
                y: Math.floor(img.height * 0.6),
                width: img.width,
                height: Math.floor(img.height * 0.4),
                desc: "屏幕下方"
            },
            // 屏幕中央区域
            {
                x: Math.floor(img.width * 0.2),
                y: Math.floor(img.height * 0.4),
                width: Math.floor(img.width * 0.6),
                height: Math.floor(img.height * 0.3),
                desc: "屏幕中央"
            }
        ];
        
        for (var i = 0; i < searchRegions.length; i++) {
            var region = searchRegions[i];
            log("🔍 搜索区域: " + region.desc);
            
            var result = findColorInRegion(img, AD_COLORS, region);
            if (result) {
                log("✅ 在" + region.desc + "找到疑似广告按钮: (" + result.x + ", " + result.y + ") 颜色: " + result.color, "SUCCESS");
                img.recycle();
                return {x: result.x, y: result.y};
            }
        }
        
        log("❌ 未找到广告按钮特征", "WARN");
        img.recycle();
        return null;
        
    } catch (e) {
        log("❌ 图像识别查找广告按钮出错: " + e.message, "ERROR");
        return null;
    }
}

// 查找关闭按钮（基于图像分析）
function findCloseButtonByImage() {
    log("🔍 使用图像识别查找关闭按钮...");
    
    try {
        var img = captureScreen();
        if (!img) {
            log("❌ 截图失败", "ERROR");
            return null;
        }
        
        // 定义搜索区域（屏幕上方，关闭按钮通常在这里）
        var searchRegions = [
            // 右上角
            {
                x: Math.floor(img.width * 0.8),
                y: 0,
                width: Math.floor(img.width * 0.2),
                height: Math.floor(img.height * 0.2),
                desc: "右上角"
            },
            // 左上角
            {
                x: 0,
                y: 0,
                width: Math.floor(img.width * 0.2),
                height: Math.floor(img.height * 0.2),
                desc: "左上角"
            },
            // 屏幕上方中央
            {
                x: Math.floor(img.width * 0.3),
                y: 0,
                width: Math.floor(img.width * 0.4),
                height: Math.floor(img.height * 0.2),
                desc: "上方中央"
            }
        ];
        
        for (var i = 0; i < searchRegions.length; i++) {
            var region = searchRegions[i];
            log("🔍 搜索关闭按钮区域: " + region.desc);
            
            var result = findColorInRegion(img, CLOSE_COLORS, region);
            if (result) {
                log("✅ 在" + region.desc + "找到疑似关闭按钮: (" + result.x + ", " + result.y + ")", "SUCCESS");
                img.recycle();
                return {x: result.x, y: result.y};
            }
        }
        
        log("❌ 未找到关闭按钮特征", "WARN");
        img.recycle();
        return null;
        
    } catch (e) {
        log("❌ 图像识别查找关闭按钮出错: " + e.message, "ERROR");
        return null;
    }
}

// 智能点击（带随机偏移）
function smartClick(x, y, description) {
    try {
        // 添加随机偏移
        var offsetX = x + random(-15, 15);
        var offsetY = y + random(-15, 15);
        
        // 确保坐标在屏幕范围内
        offsetX = Math.max(0, Math.min(device.width, offsetX));
        offsetY = Math.max(0, Math.min(device.height, offsetY));
        
        log("👆 智能点击: (" + offsetX + ", " + offsetY + ") - " + description);
        
        click(offsetX, offsetY);
        sleep(1500);
        
        log("✅ 点击成功: " + description, "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 处理广告流程
function handleAdProcess() {
    log("🎬 开始处理广告流程...");
    
    try {
        // 1. 确保在微信中
        if (!switchToWechat()) {
            return false;
        }
        
        // 2. 查找广告按钮
        var adButton = findAdButtonByImage();
        if (!adButton) {
            log("❌ 未找到广告按钮", "WARN");
            return false;
        }
        
        // 3. 点击广告按钮
        if (!smartClick(adButton.x, adButton.y, "广告按钮")) {
            return false;
        }
        
        adCount++;
        log("🎬 第" + adCount + "次广告开始", "SUCCESS");
        
        // 4. 等待广告播放
        log("⏳ 等待广告播放" + (CONFIG.adWaitTime/1000) + "秒...");
        sleep(CONFIG.adWaitTime);
        
        // 5. 查找并点击关闭按钮
        var closeButton = findCloseButtonByImage();
        if (closeButton) {
            if (smartClick(closeButton.x, closeButton.y, "关闭按钮")) {
                sleep(2000);
                successCount++;
                log("✅ 第" + adCount + "次广告完成", "SUCCESS");
                return true;
            }
        } else {
            log("⚠️ 未找到关闭按钮，尝试返回", "WARN");
            back();
            sleep(2000);
            // 即使没找到关闭按钮也算一次尝试
            return true;
        }
        
        return true;
        
    } catch (e) {
        log("❌ 处理广告流程出错: " + e.message, "ERROR");
        return false;
    }
}

// 主循环
function mainLoop() {
    log("🚀 开始图像识别版主循环", "SUCCESS");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("📍 执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 处理广告
            if (handleAdProcess()) {
                log("✅ 广告处理成功");
                errorCount = 0; // 重置错误计数
            } else {
                log("❌ 广告处理失败");
                errorCount++;
            }
            
            // 检查连续错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 连续错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("❌ 循环出错: " + e.message, "ERROR");
            
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    
    log("=== 📊 图像识别版脚本执行完成 ===", "SUCCESS");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总广告次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }
    
    toast("脚本执行完成！成功" + successCount + "次");
}

// 主程序
function main() {
    log("=== 🎯 抖音去水印小程序广告脚本 - 图像识别版 ===", "SUCCESS");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            return;
        }
        
        // 显示设备信息
        log("📱 设备: " + device.brand + " " + device.model);
        log("📱 分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 📋 图像识别版说明 ===");
        log("1. 使用图像识别技术查找按钮");
        log("2. 基于颜色特征识别广告按钮");
        log("3. 适用于微信小程序环境");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 日志保存到: " + LOG_FILE);
        
        // 延迟启动
        sleep(3000);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
        // 保持悬浮窗显示
        sleep(10000);
        
    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);
    }
}

// 启动脚本
log("🚀 准备启动图像识别版脚本...");
main();
