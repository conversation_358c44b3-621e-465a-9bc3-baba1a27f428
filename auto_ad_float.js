// ========================================
// 抖音去水印小程序广告自动刷脚本 - 悬浮窗版
// 支持切换应用的同时保持脚本运行
// 版本：v6.0 (悬浮窗版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.9, device.height * 0.6);
console.setPosition(50, 100);

console.log("🚀 悬浮窗版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 25,              // 每次操作间隔25秒
    adWaitTime: 20000,         // 广告等待时间20秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 3000,         // 等待超时时间3秒
    maxLoops: 15,              // 最大循环次数
    autoSwitchApp: true,       // 自动切换应用
    floatMode: true            // 悬浮窗模式
};

// 广告按钮关键词
var AD_KEYWORDS = [
    "观看广告获取金币", "观看广告", "看广告", "广告",
    "观看视频", "看视频", "视频广告", "激励视频",
    "获取奖励", "领取奖励", "获得金币", "赚取金币",
    "免费领取", "免费获得", "金币", "奖励", "视频"
];

// 关闭按钮关键词
var CLOSE_KEYWORDS = [
    "关闭", "跳过", "×", "X", "确定", "完成", "知道了", "好的", "继续"
];

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_float_log.txt";

// 增强的日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;
    
    // 控制台输出（悬浮窗）
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }
    
    // 文件日志
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    // Toast提示（重要信息）
    if (level === "SUCCESS" || level === "ERROR" || message.indexOf("完成") !== -1) {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            toast("请开启无障碍服务");
            return false;
        }
        
        // 检查悬浮窗权限
        if (!floaty.checkPermission()) {
            log("⚠️ 悬浮窗权限未开启", "WARN");
            floaty.requestPermission();
        }
        
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 切换到微信应用
function switchToWechat() {
    log("📱 切换到微信应用...");
    
    try {
        app.launch("com.tencent.mm");
        sleep(3000);
        
        var currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 成功切换到微信", "SUCCESS");
            return true;
        }
        
        log("❌ 切换到微信失败", "ERROR");
        return false;
        
    } catch (e) {
        log("❌ 切换到微信出错: " + e.message, "ERROR");
        return false;
    }
}

// 检查是否在目标界面
function checkTargetInterface() {
    try {
        var currentPkg = currentPackage();
        if (currentPkg !== "com.tencent.mm") {
            log("⚠️ 当前不在微信中: " + currentPkg, "WARN");
            return false;
        }
        
        // 检查小程序特征
        var indicators = ["抖音", "去水印", "金币", "观看", "广告"];
        
        for (var i = 0; i < indicators.length; i++) {
            var found = textContains(indicators[i]).findOne(1000);
            if (found) {
                log("✅ 检测到小程序特征: " + indicators[i]);
                return true;
            }
        }
        
        log("⚠️ 未检测到小程序特征", "WARN");
        return false;
        
    } catch (e) {
        log("❌ 检查目标界面出错: " + e.message, "ERROR");
        return false;
    }
}

// 智能查找广告按钮
function findAdButton() {
    log("🔍 查找广告按钮...");
    
    try {
        for (var i = 0; i < AD_KEYWORDS.length; i++) {
            var keyword = AD_KEYWORDS[i];
            
            // 多种查找方式
            var methods = [
                function() { return text(keyword).findOne(1000); },
                function() { return textContains(keyword).findOne(1000); },
                function() { return desc(keyword).findOne(1000); },
                function() { return descContains(keyword).findOne(1000); }
            ];
            
            for (var j = 0; j < methods.length; j++) {
                try {
                    var element = methods[j]();
                    if (element && element.clickable()) {
                        log("✅ 找到广告按钮: " + keyword + " -> " + (element.text() || element.desc()), "SUCCESS");
                        return element;
                    }
                } catch (e) {
                    // 继续尝试下一种方法
                }
            }
        }
        
        log("❌ 未找到广告按钮", "WARN");
        return null;
        
    } catch (e) {
        log("❌ 查找广告按钮出错: " + e.message, "ERROR");
        return null;
    }
}

// 查找关闭按钮
function findCloseButton() {
    log("🔍 查找关闭按钮...");
    
    try {
        for (var i = 0; i < CLOSE_KEYWORDS.length; i++) {
            var keyword = CLOSE_KEYWORDS[i];
            
            var element = text(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("✅ 找到关闭按钮: " + keyword);
                return element;
            }
            
            element = textContains(keyword).findOne(1000);
            if (element && element.clickable()) {
                log("✅ 找到关闭按钮(包含): " + keyword);
                return element;
            }
        }
        
        log("⚠️ 未找到关闭按钮", "WARN");
        return null;
        
    } catch (e) {
        log("❌ 查找关闭按钮出错: " + e.message, "ERROR");
        return null;
    }
}

// 安全点击
function safeClick(element, description) {
    if (!element) {
        log("❌ 元素不存在: " + description, "ERROR");
        return false;
    }
    
    try {
        if (!element.clickable()) {
            log("⚠️ 元素不可点击: " + description, "WARN");
            return false;
        }
        
        element.click();
        log("✅ 点击成功: " + description, "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 处理广告流程
function handleAdProcess() {
    log("🎬 开始处理广告流程...");
    
    try {
        // 1. 确保在正确界面
        if (!checkTargetInterface()) {
            if (CONFIG.autoSwitchApp) {
                if (!switchToWechat()) {
                    return false;
                }
                sleep(2000);
                
                if (!checkTargetInterface()) {
                    log("❌ 切换后仍未检测到目标界面", "ERROR");
                    return false;
                }
            } else {
                return false;
            }
        }
        
        // 2. 查找并点击广告按钮
        var adBtn = findAdButton();
        if (!adBtn) {
            return false;
        }
        
        if (!safeClick(adBtn, "广告按钮")) {
            return false;
        }
        
        adCount++;
        log("🎬 第" + adCount + "次广告开始", "SUCCESS");
        
        // 3. 等待广告播放
        log("⏳ 等待广告播放...");
        sleep(CONFIG.adWaitTime);
        
        // 4. 尝试关闭广告
        var closeBtn = findCloseButton();
        if (closeBtn) {
            if (safeClick(closeBtn, "关闭按钮")) {
                sleep(2000);
                successCount++;
                log("✅ 第" + adCount + "次广告完成", "SUCCESS");
                return true;
            }
        } else {
            log("⚠️ 未找到关闭按钮，尝试返回", "WARN");
            back();
            sleep(2000);
        }
        
        return true;
        
    } catch (e) {
        log("❌ 处理广告流程出错: " + e.message, "ERROR");
        return false;
    }
}

// 主循环
function mainLoop() {
    log("🚀 开始悬浮窗版主循环", "SUCCESS");
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("📍 执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 处理广告
            if (handleAdProcess()) {
                log("✅ 广告处理成功");
            } else {
                log("❌ 广告处理失败");
                errorCount++;
            }
            
            // 检查错误次数
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            errorCount++;
            log("❌ 循环出错: " + e.message, "ERROR");
            
            if (errorCount >= CONFIG.maxRetries) {
                log("❌ 错误次数过多，停止脚本", "ERROR");
                break;
            }
            
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    
    log("=== 📊 悬浮窗版脚本执行完成 ===", "SUCCESS");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总广告次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("❌ 失败次数: " + errorCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }
    
    toast("脚本执行完成！成功" + successCount + "次");
}

// 主程序
function main() {
    log("=== 🎯 抖音去水印小程序广告脚本 - 悬浮窗版 ===", "SUCCESS");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            return;
        }
        
        // 显示设备信息
        log("📱 设备: " + device.brand + " " + device.model);
        log("📱 分辨率: " + device.width + "x" + device.height);
        
        // 使用说明
        log("=== 📋 悬浮窗版使用说明 ===");
        log("1. 悬浮窗将显示实时日志");
        log("2. 可以切换到其他应用");
        log("3. 脚本在后台自动运行");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 日志保存到: " + LOG_FILE);
        
        // 延迟启动
        sleep(3000);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
        // 保持悬浮窗显示结果
        sleep(10000);
        
    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);
    }
}

// 启动脚本
log("🚀 准备启动悬浮窗版脚本...");
main();
