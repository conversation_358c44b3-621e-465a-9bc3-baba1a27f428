<!--index.wxml-->
<view class="container">
  <view class="header">
    <text class="title">抖音去水印下载</text>
    <view class="coins-display">
      <text class="coins-icon">💰</text>
      <text class="coins-text">{{coins}}/{{maxCoins}}</text>
    </view>
  </view>

  <view class="content">
    <view class="title-area">
      <view class="main-title">抖音视频无水印下载</view>
      <view class="sub-title">快速提取原始高清视频</view>
    </view>

    <view class="input-group">
      <view class="input-area">
        <input class="url-input" value="{{shareUrl}}" placeholder="请输入抖音分享链接" bindinput="onInputChange" />
        <view class="paste-btn" bindtap="pasteUrl">粘贴</view>
      </view>
      <button class="action-btn" bindtap="parseVideo" loading="{{loading}}">一键解析</button>
      <button class="ad-btn" bindtap="watchAd" loading="{{adLoading}}" disabled="{{coins >= maxCoins}}">
        <text class="ad-icon">📺</text>
        <text class="ad-text">{{coins >= maxCoins ? '金币已满' : '观看广告获取金币'}}</text>
      </button>
    </view>

    <!-- 解析结果区域 - 直接在页面中显示 -->
    <view class="result-section" wx:if="{{videoInfo && !loading}}">
      <view class="result-header">
        <text class="result-title">解析成功</text>
      </view>
      
      <view class="video-container">
        <video 
          wx:if="{{videoInfo.video_url}}" 
          src="{{videoInfo.video_url}}" 
          poster="{{videoInfo.cover_url}}"
          controls
        ></video>
        
        <swiper wx:if="{{videoInfo.images && videoInfo.images.length > 0}}" 
          indicator-dots="{{true}}">
          <block wx:for="{{videoInfo.images}}" wx:key="index">
            <swiper-item>
              <image src="{{item}}" mode="aspectFit"></image>
            </swiper-item>
          </block>
        </swiper>
      </view>
      
      <view class="video-info">
        <view class="video-title">{{videoInfo.title || '无标题'}}</view>
        <view class="author">
          <image src="{{videoInfo.author.avatar || '/images/default-avatar.png'}}"></image>
          <text>{{videoInfo.author.name || '未知作者'}}</text>
        </view>
      </view>
      
      <view class="result-actions">
        <button bindtap="copyVideoUrl">浏览器下载</button>
        <button bindtap="saveToAlbum" class="primary">保存到相册</button>
        <button bindtap="shareVideo" open-type="share">分享</button>
      </view>
    </view>

    <view class="divider" wx:if="{{!videoInfo || loading}}">
      <text class="divider-text">使用说明</text>
    </view>

    <view class="steps" wx:if="{{!videoInfo || loading}}">
      <view class="step">
        <view class="step-num">1</view>
        <view class="step-text">打开抖音，点击要下载的视频右侧的"分享"按钮</view>
      </view>
      <view class="step">
        <view class="step-num">2</view>
        <view class="step-text">选择"复制链接"选项</view>
      </view>
      <view class="step">
        <view class="step-num">3</view>
        <view class="step-text">返回本小程序，点击粘贴链接，再点击"一键解析"</view>
      </view>
      <view class="step">
        <view class="step-num">4</view>
        <view class="step-text">解析成功后点击"保存"按钮下载无水印视频</view>
      </view>
      <view class="step">
        <view class="step-num">5</view>
        <view class="step-text">视频将保存到您的手机相册中，可随时查看分享</view>
      </view>
    </view>

    <view class="error" wx:if="{{errorMsg}}">{{errorMsg}}</view>
  </view>

  <view class="footer">
    <text>© {{currentYear}} 抖音去水印助手</text>
  </view>
  
  <!-- 加载中 -->
  <view class="loading" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <view class="loading-text">视频解析中...</view>
  </view>
</view>
