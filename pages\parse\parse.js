// pages/parse/parse.js
// import {util} from "../../utils/util.js"
const DownloadSaveFile = require('../../utils/util.js');
import {downLoadVideo} from "../../utils/download.js";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    url:"",
    result: null,
    inputValue:'',
    canIUseClipboard: wx.canIUse('setClipboardData')
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // this.parse('https://v.douyin.com/ify4L9Eo/');
    // this.parse('https://www.kuaishou.com/f/X-2rtuREwE6lu2c2');
  },
  getInputValue:function(e){
    const value = e.detail.value;
    this.setData({
      url:value
    })
  },
  verifyAndRequest:function(){
    var url = this.data.url;
    // console.log(url);
    var pattern = new RegExp("(https{0,1}://.*?douyin\.com\/[a-zA-Z0-9]+)");
    var patternKuaishou = new RegExp("(https{0,1}://.*?kuaishou\.com\/[a-zA-Z0-9]+)");
    // var patternKuaishou = new RegExp(/(https*:\/\/v\.kuaishou\.com\/[a-zA-Z0-9]{6})/);
    
    if (pattern.test(url)){
      this.parse(RegExp.$1)
    }else if(patternKuaishou.test(url)){
      wx.showToast({
        title: '现暂不支持其他平台，开发中',
      })      
      // console.log(RegExp.$1);
      // this.parseKuaishou(RegExp.$1);
    }else{
      console.log("输入正确的url")
      wx.showToast({
        title: '输入url错误',
      })
    }
  },
  parse: function(url) {    
    var that = this;
    wx.showLoading({
      title: '正在解析',
      mask: true
    });
    wx.request({
        url: 'https://www.ziyuewanfeng.xyz/api/douyin/parse', // 你的Flask服务器地址
        // url: 'http://8.134.122.92/api/douyin/parse', // 你的Flask服务器地址
        method: 'POST',
        data: {
            url: url
        },
        header: {
            'content-type': 'application/json'
        },
        success(res) {
            console.log("请求Flask接口成功", res.data);
            // if (res.statusCode === 200 && res.data.success) {
            if (res.statusCode === 200){
              // console.log(res.data.final_url.length );
                that.setData({
                    // result: res.data.data                    
                    result: res.data.data
                    
                });
                wx.hideLoading();
            } else {
                console.log("解析失败", res.data.message);
                wx.showToast({
                    title: res.data.message || '解析失败',
                    icon: 'none'
                });
            }
        },
        fail(err) {
            console.log("请求Flask接口失败", err);
            wx.showToast({
                title: '网络请求失败',
                icon: 'none'
            });
        }
    });
},

  parseOld: function(url) {
    var that = this;
    wx.cloud.callFunction({
      name: "parseVideo",
      data: {
        "url": url
      },
      success(res) {
        console.log("云函数获取数据成功", res)
        that.setData({
          result: res.result
        })
      },
      fail(err) {
        console.log("云函数获取数据失败", err)
      }
    })
  },
  parseKuaishou: function(url){
    var that = this;
    wx.showLoading({
      title: '正在解析',
      mask: true
    });
    wx.request({
      url: 'https://www.ziyuewanfeng.xyz/api/douyin/parse', // 你的Flask服务器地址
      // url: 'http://8.134.122.92/api/douyin/parse', // 你的Flask服务器地址
      method: 'POST',
      data: {
          url: url
      },
      header: {
          'content-type': 'application/json'
      },
      success(res) {
          console.log("请求Flask接口成功", res.data);
          // if (res.statusCode === 200 && res.data.success) {
          if (res.statusCode === 200){
            // console.log(res.data.final_url.length );
              that.setData({
                  // result: res.data.data                    
                  result: res.data.data
                  
              });
              wx.hideLoading();
          } else {
              console.log("解析失败", res.data.message);
              wx.showToast({
                  title: res.data.message || '解析失败',
                  icon: 'none'
              });
          }
      },
      fail(err) {
          console.log("请求Flask接口失败", err);
          wx.showToast({
              title: '网络请求失败',
              icon: 'none'
          });
      }
    });
  },
  parseKuaishouOld: function(url){
    var that = this;
    wx.cloud.callFunction({
      name: "parseKuaiShou",
      data: {
        "url": url
      },
      success(res) {
        console.log("云函数获取数据成功", res)
        that.setData({
          result: res.result
        })
      },
      fail(err) {
        console.log("云函数获取数据失败", err)
      }
    })
  },
  copyText: function() {
    console.log(this.data.result.playAddress)
    wx.setClipboardData({
    data: this.data.result.playAddress,
      success: function () {
        wx.showToast({
          title: '复制成功',
        })
      },
      fail: function(){
        console.log("复制失败")
      }
    })
  },
  saveVideo: function(){
    var tempUrl = this.data.result.playAddress;
    if (tempUrl.search("https") == -1){
      tempUrl = tempUrl.replace('http',"https");
    }
    console.log(tempUrl);
    downLoadVideo(tempUrl);
    // DownloadSaveFile.downloadFile("video",tempUrl)
  },
  clearText: function(){
    this.setData({
      result:null,
      inputValue:"",
      url:""
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})