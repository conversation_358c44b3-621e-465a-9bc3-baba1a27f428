// ========================================
// 抖音去水印小程序广告自动刷脚本 - 简化点击版
// 用户手动打开小程序，脚本只负责点击广告
// 版本：v10.0 (简化点击版)
// ========================================

// 启用悬浮窗控制台
console.show();
console.setSize(device.width * 0.8, device.height * 0.5);
console.setPosition(100, 200);

console.log("🚀 简化点击版脚本启动...");

// 配置参数
var CONFIG = {
    interval: 30,              // 每次操作间隔30秒
    adWaitTime: 25000,         // 广告等待时间25秒
    maxLoops: 20,              // 最大循环次数
    clickDelay: 1500,          // 点击后延迟
    screenWidth: device.width,
    screenHeight: device.height
};

// 广告按钮可能的位置（按屏幕比例）
var AD_POSITIONS = [
    // 屏幕下方中央（最常见的广告按钮位置）
    {x: 0.5, y: 0.8, desc: "屏幕下方中央"},
    {x: 0.5, y: 0.75, desc: "屏幕中下部"},
    {x: 0.5, y: 0.85, desc: "屏幕底部"},
    
    // 屏幕下方左右两侧
    {x: 0.3, y: 0.8, desc: "屏幕下方左侧"},
    {x: 0.7, y: 0.8, desc: "屏幕下方右侧"},
    
    // 屏幕中央区域
    {x: 0.5, y: 0.6, desc: "屏幕中央"},
    {x: 0.5, y: 0.65, desc: "屏幕中央偏下"},
    
    // 其他可能位置
    {x: 0.2, y: 0.7, desc: "左侧中下"},
    {x: 0.8, y: 0.7, desc: "右侧中下"}
];

// 关闭按钮可能的位置
var CLOSE_POSITIONS = [
    // 右上角（最常见）
    {x: 0.9, y: 0.1, desc: "右上角"},
    {x: 0.85, y: 0.15, desc: "右上角内侧"},
    
    // 左上角
    {x: 0.1, y: 0.1, desc: "左上角"},
    {x: 0.15, y: 0.15, desc: "左上角内侧"},
    
    // 屏幕上方中央
    {x: 0.5, y: 0.1, desc: "上方中央"},
    {x: 0.5, y: 0.15, desc: "上方中央下移"},
    
    // 屏幕中央（某些广告的关闭按钮）
    {x: 0.5, y: 0.5, desc: "屏幕中央"}
];

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var skipCount = 0;

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_simple_click_log.txt";

// 日志函数
function log(message, level) {
    level = level || "INFO";
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "][" + level + "] " + message;
    
    if (level === "ERROR") {
        console.error(logMsg);
    } else if (level === "WARN") {
        console.warn(logMsg);
    } else if (level === "SUCCESS") {
        console.info("✅ " + message);
    } else {
        console.log(logMsg);
    }
    
    try {
        files.append(LOG_FILE, logMsg + "\n");
    } catch (e) {
        // 忽略文件写入错误
    }
    
    if (level === "SUCCESS" || level === "ERROR") {
        toast(message);
    }
}

// 权限检查
function checkPermissions() {
    log("🔍 检查权限...");
    
    try {
        if (!auto.service) {
            log("❌ 请开启无障碍服务", "ERROR");
            return false;
        }
        log("✅ 权限检查通过", "SUCCESS");
        return true;
    } catch (e) {
        log("❌ 权限检查失败: " + e.message, "ERROR");
        return false;
    }
}

// 转换比例坐标为实际坐标
function getActualCoordinate(ratioX, ratioY) {
    return {
        x: Math.floor(CONFIG.screenWidth * ratioX),
        y: Math.floor(CONFIG.screenHeight * ratioY)
    };
}

// 智能点击函数
function smartClick(ratioX, ratioY, description) {
    try {
        var coord = getActualCoordinate(ratioX, ratioY);
        
        // 添加随机偏移，模拟真实点击
        var offsetX = coord.x + random(-20, 20);
        var offsetY = coord.y + random(-20, 20);
        
        // 确保坐标在屏幕范围内
        offsetX = Math.max(50, Math.min(CONFIG.screenWidth - 50, offsetX));
        offsetY = Math.max(50, Math.min(CONFIG.screenHeight - 50, offsetY));
        
        log("👆 点击: (" + offsetX + ", " + offsetY + ") - " + description);
        
        click(offsetX, offsetY);
        sleep(CONFIG.clickDelay);
        
        return true;
    } catch (e) {
        log("❌ 点击失败: " + description + " - " + e.message, "ERROR");
        return false;
    }
}

// 尝试点击广告按钮
function tryClickAdButton() {
    log("🎯 尝试点击广告按钮...");
    
    for (var i = 0; i < AD_POSITIONS.length; i++) {
        var pos = AD_POSITIONS[i];
        log("🎯 尝试位置 " + (i + 1) + "/" + AD_POSITIONS.length + ": " + pos.desc);
        
        if (smartClick(pos.x, pos.y, pos.desc)) {
            sleep(2000);
            
            // 简单检查：如果当前不在微信，可能进入了广告
            try {
                var currentPkg = currentPackage();
                if (currentPkg !== "com.tencent.mm") {
                    log("✅ 可能进入了广告应用: " + currentPkg, "SUCCESS");
                    return true;
                }
            } catch (e) {
                // 忽略包名检查错误
            }
            
            // 继续尝试下一个位置
            sleep(1000);
        }
    }
    
    log("⚠️ 所有广告位置都尝试完毕", "WARN");
    return false;
}

// 尝试关闭广告
function tryCloseAd() {
    log("❌ 尝试关闭广告...");
    
    for (var i = 0; i < CLOSE_POSITIONS.length; i++) {
        var pos = CLOSE_POSITIONS[i];
        log("❌ 尝试关闭位置 " + (i + 1) + "/" + CLOSE_POSITIONS.length + ": " + pos.desc);
        
        if (smartClick(pos.x, pos.y, pos.desc)) {
            sleep(2000);
            
            // 检查是否回到微信
            try {
                var currentPkg = currentPackage();
                if (currentPkg === "com.tencent.mm") {
                    log("✅ 成功关闭广告，回到微信", "SUCCESS");
                    return true;
                }
            } catch (e) {
                // 忽略包名检查错误
            }
            
            sleep(1000);
        }
    }
    
    // 尝试返回键
    log("🔙 尝试使用返回键...");
    back();
    sleep(2000);
    
    try {
        var currentPkg = currentPackage();
        if (currentPkg === "com.tencent.mm") {
            log("✅ 返回键成功关闭广告", "SUCCESS");
            return true;
        }
    } catch (e) {
        // 忽略包名检查错误
    }
    
    log("⚠️ 广告关闭尝试完毕", "WARN");
    return true; // 即使关闭失败也继续
}

// 单次广告处理流程
function handleSingleAd() {
    log("🎬 开始处理广告...");
    
    try {
        // 1. 尝试点击广告按钮
        if (!tryClickAdButton()) {
            skipCount++;
            log("⚠️ 未能点击广告按钮，跳过本次", "WARN");
            return false;
        }
        
        adCount++;
        log("🎬 第" + adCount + "次广告开始", "SUCCESS");
        
        // 2. 等待广告播放
        log("⏳ 等待广告播放" + (CONFIG.adWaitTime/1000) + "秒...");
        sleep(CONFIG.adWaitTime);
        
        // 3. 尝试关闭广告
        if (tryCloseAd()) {
            successCount++;
            log("✅ 第" + adCount + "次广告完成", "SUCCESS");
            return true;
        }
        
        return true;
        
    } catch (e) {
        log("❌ 处理广告出错: " + e.message, "ERROR");
        return false;
    }
}

// 主循环
function mainLoop() {
    log("🚀 开始简化点击版主循环", "SUCCESS");
    log("📋 请确保已手动打开抖音去水印小程序");
    
    // 给用户时间准备
    for (var countdown = 5; countdown > 0; countdown--) {
        log("⏰ " + countdown + "秒后开始执行...");
        sleep(1000);
    }
    
    for (var i = 0; i < CONFIG.maxLoops; i++) {
        try {
            log("📍 执行循环 " + (i + 1) + "/" + CONFIG.maxLoops);
            
            // 处理广告
            if (handleSingleAd()) {
                log("✅ 本轮处理完成");
            } else {
                log("❌ 本轮处理失败");
            }
            
            // 等待间隔
            if (i < CONFIG.maxLoops - 1) {
                log("⏱️ 等待" + CONFIG.interval + "秒后继续...");
                sleep(CONFIG.interval * 1000);
            }
            
        } catch (e) {
            log("❌ 循环出错: " + e.message, "ERROR");
            sleep(5000);
        }
    }
}

// 显示结果
function showResults() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    
    log("=== 📊 简化点击版脚本执行完成 ===", "SUCCESS");
    log("⏰ 总运行时间: " + minutes + "分" + seconds + "秒");
    log("🎬 总广告次数: " + adCount + "次");
    log("✅ 成功次数: " + successCount + "次");
    log("⏭️ 跳过次数: " + skipCount + "次");
    
    if (adCount > 0) {
        var successRate = Math.round((successCount / adCount) * 100);
        log("📈 成功率: " + successRate + "%");
    }
    
    toast("脚本执行完成！成功" + successCount + "次");
}

// 主程序
function main() {
    log("=== 🎯 抖音去水印小程序广告脚本 - 简化点击版 ===", "SUCCESS");
    
    try {
        // 检查权限
        if (!checkPermissions()) {
            return;
        }
        
        // 显示设备信息
        log("📱 设备: " + device.brand + " " + device.model);
        log("📱 分辨率: " + CONFIG.screenWidth + "x" + CONFIG.screenHeight);
        
        // 使用说明
        log("=== 📋 简化点击版说明 ===");
        log("1. 请手动打开微信中的抖音去水印小程序");
        log("2. 确保在小程序主界面");
        log("3. 脚本将自动尝试点击广告按钮");
        log("4. 最多执行" + CONFIG.maxLoops + "次循环");
        log("5. 日志保存到: " + LOG_FILE);
        
        // 开始主循环
        mainLoop();
        
        // 显示结果
        showResults();
        
        // 保持悬浮窗显示
        sleep(10000);
        
    } catch (e) {
        log("❌ 主程序出错: " + e.message, "ERROR");
        console.error("错误堆栈: " + e.stack);
    }
}

// 启动脚本
log("🚀 准备启动简化点击版脚本...");
main();
