// promise风格

//检查权限，支持授权后自动重试
function checkAuth() {
	return new Promise((resolve, reject) => {
		wx.getSetting({
			success(res) {
				if (!res.authSetting['scope.writePhotosAlbum']) {
					//请求授权
					wx.authorize({
						scope: 'scope.writePhotosAlbum',
						success() {
							resolve('授权成功');
						},
						fail() {
							// 用户拒绝授权，弹窗引导去设置页
							wx.showModal({
								title: '提示',
								content: '保存到系统相册需要授权，是否去设置开启？',
								confirmText: '去设置',
								cancelText: '取消',
								success(modalRes) {
									if (modalRes.confirm) {
										wx.openSetting({
											success(settingRes) {
												if (settingRes.authSetting['scope.writePhotosAlbum']) {
													resolve('授权成功');
												} else {
													wx.showToast({
														title: '未获得授权',
														icon: 'none',
													});
													reject('未获得授权');
												}
											},
											fail() {
												wx.showToast({
													title: '打开设置页失败',
													icon: 'none',
												});
												reject('打开设置页失败');
											}
										});
									} else {
										wx.showToast({
											title: '未获得授权',
											icon: 'none',
										});
										reject('未获得授权');
									}
								}
							});
						}
					});
				} else {
					//已有授权
					resolve('授权成功');
				}
			},
			fail() {
				wx.showToast({
					title: '获取授权失败',
					icon: 'none',
				});
				reject('获取授权失败');
			}
		});
	});
}

const downLoadVideo = function (url) {
	wx.showLoading({
		title: '正在下载',
		mask: true
	});
	checkAuth()
		.then(() => {
			wx.downloadFile({
				url: url,
				success: res => {
					if (res.statusCode === 200) {
						wx.saveVideoToPhotosAlbum({
							filePath: res.tempFilePath,
							success: res2 => {
								wx.hideLoading();
								wx.showToast({
									title: '下载成功',
								});
							},
							fail: err => {
								wx.hideLoading();
								// 详细提示错误原因
								wx.showModal({
									title: '保存失败',
									content: err.errMsg || '保存视频失败',
									showCancel: false
								});
							}
						});
					} else {
						wx.hideLoading();
						wx.showToast({
							title: '下载失败',
							icon: 'none'
						});
					}
				},
				fail: res => {
					wx.hideLoading();
					wx.showToast({
						title: '下载失败',
						icon: 'none'
					});
				}
			});
		})
		.catch(() => {
			wx.hideLoading();
			// 授权失败已在 checkAuth 里提示
		});
};

module.exports = {
	downLoadVideo
}