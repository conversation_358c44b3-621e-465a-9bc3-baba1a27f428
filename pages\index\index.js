//index.js
//获取应用实例
const app = getApp()

// 若在开发者工具中无法预览广告，请切换开发者工具中的基础库版本
// 在页面中定义激励视频广告
let videoAd = null

Page({
  data: {
    shareUrl: '',
    videoInfo: null,
    loading: false,
    errorMsg: '',
    currentYear: new Date().getFullYear(),
    // 金币系统
    coins: 0,
    maxCoins: 5000,
    adLoading: false
  },

  onLoad() {
    // 初始化金币系统
    this.initCoins();
    // 初始化激励视频广告
    this.initRewardedVideoAd();
    // 检查剪贴板
    this.checkClipboard();
  },

  // 初始化金币系统
  initCoins() {
    // 获取当前日期
    const today = new Date().toDateString();
    const lastDate = wx.getStorageSync('last_coin_date') || '';

    // 检查是否是新的一天
    if (today !== lastDate) {
      // 新的一天，清零金币
      console.log('新的一天，金币清零');
      wx.setStorageSync('user_coins', 0);
      wx.setStorageSync('last_coin_date', today);
      this.setData({
        coins: 0
      });
    } else {
      // 同一天，加载之前的金币数量
      const coins = wx.getStorageSync('user_coins') || 0;
      this.setData({
        coins: Math.min(coins, this.data.maxCoins)
      });
    }
  },

  // 初始化激励视频广告
  initRewardedVideoAd() {
    // 在页面onLoad回调事件中创建激励视频广告实例
    if (wx.createRewardedVideoAd) {
      videoAd = wx.createRewardedVideoAd({
        adUnitId: 'adunit-921052c81785daf8'
      });

      videoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });

      videoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
        wx.showToast({
          title: '广告加载失败',
          icon: 'none'
        });
      });

      videoAd.onClose((res) => {
        // 重置加载状态
        this.setData({
          adLoading: false
        });

        // 用户点击了【关闭广告】按钮
        if (res && res.isEnded) {
          // 正常播放结束，可以下发游戏奖励
          this.rewardCoins(this.adCallback);
        } else {
          // 播放中途退出，不下发游戏奖励
          wx.showToast({
            title: '请观看完整广告才能获得奖励',
            icon: 'none'
          });
        }
      });
    } else {
      console.log('当前微信版本过低，无法使用激励视频广告');
    }
  },

  // 观看广告获取金币
  watchAd(callback) {
    if (this.data.coins >= this.data.maxCoins && !callback) {
      wx.showToast({
        title: '金币已达上限',
        icon: 'none'
      });
      return;
    }

    // 保存回调函数
    this.adCallback = callback;

    this.setData({
      adLoading: true
    });

    // 用户触发广告后，显示激励视频广告
    if (videoAd) {
      videoAd.show().catch(() => {
        // 失败重试
        videoAd.load()
          .then(() => videoAd.show())
          .catch(err => {
            console.error('激励视频广告显示失败', err);
            this.setData({
              adLoading: false
            });
            wx.showToast({
              title: '广告显示失败，请稍后重试',
              icon: 'none'
            });
            // 广告失败，清除回调
            this.adCallback = null;
          });
      });
    } else {
      this.setData({
        adLoading: false
      });
      wx.showToast({
        title: '广告功能不可用',
        icon: 'none'
      });
      // 广告不可用，清除回调
      this.adCallback = null;
    }
  },

  // 奖励金币
  rewardCoins(callback) {
    // 检查是否是新的一天，如果是则先清零
    const today = new Date().toDateString();
    const lastDate = wx.getStorageSync('last_coin_date') || '';

    let currentCoins = this.data.coins;
    if (today !== lastDate) {
      // 新的一天，清零金币
      console.log('奖励时发现新的一天，金币清零');
      currentCoins = 0;
      wx.setStorageSync('user_coins', 0);
      wx.setStorageSync('last_coin_date', today);
      this.setData({
        coins: 0
      });
    }

    if (currentCoins >= this.data.maxCoins) {
      wx.showToast({
        title: '金币已达上限',
        icon: 'none'
      });
      return;
    }

    // 实际只累计1个金币
    const actualRewardAmount = 1;
    const newCoins = Math.min(currentCoins + actualRewardAmount, this.data.maxCoins);

    // 更新数据
    this.setData({
      coins: newCoins
    });

    // 保存到本地存储
    wx.setStorageSync('user_coins', newCoins);

    // 显示给用户的是随机金币数量（1-10个）
    const displayRewardAmount = Math.floor(Math.random() * 10) + 1;

    wx.showModal({
      title: '观看完成',
      content: `恭喜获得 ${displayRewardAmount} 个金币！`,
      showCancel: false,
      confirmText: '太好了',
      success: () => {
        // 执行回调
        if (callback) {
          callback();
        }
      }
    });
  },

  // 检查剪贴板内容
  checkClipboard() {
    wx.getClipboardData({
      success: (res) => {
        const clipData = res.data;
        // 检查是否包含抖音链接的特征
        if (clipData && (clipData.indexOf('douyin.com') > -1 || 
                          clipData.indexOf('iesdouyin.com') > -1)) {
          wx.showModal({
            title: '发现抖音链接',
            content: '剪贴板中发现抖音分享链接，是否直接解析？',
            confirmText: '立即解析',
            success: (res) => {
              if (res.confirm) {
                this.setData({
                  shareUrl: clipData
                });
                this.parseVideo();
              }
            }
          });
        }
      }
    });
  },

  // 当输入框内容变化时调用
  onInputChange(e) {
    this.setData({
      shareUrl: e.detail.value,
      errorMsg: '' // 清除错误信息
    });
  },

  // 从剪贴板粘贴内容
  pasteUrl() {
    wx.getClipboardData({
      success: (res) => {
        if (res.data) {
          this.setData({
            shareUrl: res.data,
            errorMsg: ''
          });
        } else {
          wx.showToast({
            title: '剪贴板为空',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取剪贴板内容失败',
          icon: 'none'
        });
      }
    });
  },

  // 清除输入框内容
  clearInput() {
    this.setData({
      shareUrl: '',
      errorMsg: ''
    });
  },

  // 重置解析结果
  resetResult() {
    this.setData({
      videoInfo: null
    });
    
    // 滚动到顶部
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 300
    });
  },

  // 检查金币并触发广告
  checkCoinsAndShowAd(callback) {
    if (this.data.coins <= 0) {
      // 金币不足，显示广告
      this.watchAd(callback);
    } else {
      // 金币充足，直接执行
      callback();
    }
  },

  // 扣除金币
  deductCoins(amount = 1) {
    const currentCoins = this.data.coins;
    const newCoins = Math.max(currentCoins - amount, 0);

    this.setData({
      coins: newCoins
    });

    // 保存到本地存储
    wx.setStorageSync('user_coins', newCoins);

    return newCoins;
  },

  // 解析视频
  parseVideo() {
    // 检查金币，如果为0则先看广告
    this.checkCoinsAndShowAd(() => {
      this.doParseVideo();
    });
  },

  // 执行解析视频
  doParseVideo() {
    // 获取输入的分享链接
    const shareUrl = this.data.shareUrl;

    // 验证输入
    if (!shareUrl || shareUrl.trim() === '') {
      this.setData({
        errorMsg: '请输入分享链接'
      });
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true,
      errorMsg: '',
      videoInfo: null
    });

    // 调用API解析视频
    wx.request({
      url: 'https://www.ziyuewanfeng.xyz/api/v1/parse',
      method: 'GET',
      data: {
        url: shareUrl,
        direct: false
      },
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('API返回结果:', res);
        
        if (res.statusCode === 200 && res.data.data) {
          // 解析成功，获取data字段
          const apiData = res.data.data;
          
          // 格式化数据以符合我们的videoInfo结构
          this.setData({
            videoInfo: {
              video_url: apiData.playAddress || apiData.video_url || '',
              cover_url: apiData.coverImage || apiData.cover_url || '',
              title: apiData.desc || apiData.title || '',
              author: {
                name: apiData.author?.name || apiData.author || '未知作者',
                avatar: apiData.authorImg || (apiData.author ? apiData.author.avatar : '') || ''
              },
              images: apiData.images || []
            },
            errorMsg: ''
          });
          
          // 扣除金币
          const remainingCoins = this.deductCoins(1);

          // 解析成功后滚动到结果区域
          setTimeout(() => {
            wx.pageScrollTo({
              selector: '.result-section',
              offsetTop: -20,
              duration: 300
            });

            // 显示金币扣除提示
            setTimeout(() => {
              wx.showToast({
                title: `解析成功！消耗1金币，剩余${remainingCoins}个`,
                icon: 'none',
                duration: 2000
              });
            }, 500);
          }, 300);
        } else {
          // 解析失败
          this.setData({
            errorMsg: res.data.msg || res.data.message || '解析失败，请检查链接是否有效'
          });
        }
      },
      fail: (error) => {
        console.error('请求失败:', error);
        this.setData({
          errorMsg: '网络请求失败，请检查网络连接'
        });
      },
      complete: () => {
        this.setData({
          loading: false
        });
      }
    });
  },

  // 显示手动下载引导
  showManualDownloadGuide(mediaType, url) {
    wx.showModal({
      title: `${mediaType}保存失败`,
      content: `由于网络或权限问题，${mediaType}保存失败。\n\n建议使用浏览器手动下载：\n1. 点击"复制链接"按钮\n2. 打开浏览器粘贴链接\n3. 长按${mediaType}保存到相册`,
      showCancel: true,
      cancelText: '取消',
      confirmText: '复制链接',
      confirmColor: '#07c160',
      success: (res) => {
        if (res.confirm) {
          // 用户选择复制链接
          wx.setClipboardData({
            data: url,
            success: () => {
              wx.showToast({
                title: '链接已复制，请在浏览器中打开',
                icon: 'none',
                duration: 3000
              });
            },
            fail: () => {
              wx.showToast({
                title: '复制失败，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 复制链接并提示浏览器下载
  copyVideoUrl() {
    const videoInfo = this.data.videoInfo;
    if (!videoInfo) return;

    // 优先复制视频链接，如果没有视频则复制第一张图片
    const urlToCopy = videoInfo.video_url || (videoInfo.images && videoInfo.images.length > 0 ? videoInfo.images[0] : '');
    const isVideo = !!videoInfo.video_url;
    const mediaType = isVideo ? '视频' : '图片';

    if (urlToCopy) {
      wx.setClipboardData({
        data: urlToCopy,
        success: () => {
          // 根据媒体类型显示不同的下载指引
          const content = isVideo ?
            `${mediaType}链接已复制！由于微信限制，请按以下步骤下载：\n\n📱 手机操作：\n1. 打开浏览器（Safari/Chrome等）\n2. 粘贴链接到地址栏访问\n3. 长按视频选择"保存视频"\n\n💻 电脑操作：\n1. 打开电脑浏览器\n2. 粘贴链接访问\n3. 右键视频选择"视频另存为"\n\n💡 推荐使用Safari浏览器效果更佳` :
            `${mediaType}链接已复制！请按以下步骤保存：\n\n📱 手机操作：\n1. 打开浏览器（Safari/Chrome等）\n2. 粘贴链接到地址栏访问\n3. 长按图片选择"保存图片"\n\n💻 电脑操作：\n1. 打开电脑浏览器\n2. 粘贴链接访问\n3. 右键图片选择"图片另存为"`;

          wx.showModal({
            title: '🔗 下载指引',
            content: content,
            showCancel: true,
            cancelText: '知道了',
            confirmText: '再次复制',
            confirmColor: '#07c160',
            success: (res) => {
              if (res.confirm) {
                // 用户选择再次复制
                wx.setClipboardData({
                  data: urlToCopy,
                  success: () => {
                    wx.showToast({
                      title: '链接已再次复制',
                      icon: 'success'
                    });
                  }
                });
              }
            }
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败，请重试',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无可下载的链接',
        icon: 'none'
      });
    }
  },

  // 保存到相册
  saveToAlbum() {
    const videoInfo = this.data.videoInfo;
    if (!videoInfo) return;

    // 检查金币，如果为0则先看广告
    this.checkCoinsAndShowAd(() => {
      this.doSaveToAlbum();
    });
  },

  // 执行保存到相册
  doSaveToAlbum() {
    const videoInfo = this.data.videoInfo;
    if (!videoInfo) return;
    
    // 检查授权状态并请求授权
    const checkAndRequestPermission = (mediaType, callback) => {
      // 检查授权状态
      wx.getSetting({
        success: (res) => {
          const scopeName = 'scope.writePhotosAlbum';
          if (!res.authSetting[scopeName]) {
            // 如果未授权，申请授权
            wx.authorize({
              scope: scopeName,
              success: () => {
                // 授权成功，执行回调
                callback();
              },
              fail: () => {
                // 用户拒绝授权，引导用户打开设置页
                wx.showModal({
                  title: '提示',
                  content: '需要您授权保存到相册',
                  confirmText: '去设置',
                  cancelText: '取消',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting({
                        success: (settingRes) => {
                          if (settingRes.authSetting[scopeName]) {
                            callback();
                          }
                        }
                      });
                    }
                  }
                });
              }
            });
          } else {
            // 已授权，直接执行回调
            callback();
          }
        }
      });
    };
    
    // 视频需要先下载再保存
    if (videoInfo.video_url) {
      checkAndRequestPermission('video', () => {
        // 显示初始进度
        wx.showLoading({
          title: '下载进度 0%',
        });

        // 下载视频，使用真实进度回调
        const downloadTask = wx.downloadFile({
          url: videoInfo.video_url,
          success: (res) => {
            if (res.statusCode === 200) {
              // 下载成功，开始保存
              wx.showLoading({
                title: '正在保存到相册...',
              });

              // 保存视频到相册
              wx.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  // 扣除金币
                  const remainingCoins = this.deductCoins(1);

                  wx.hideLoading();
                  wx.showToast({
                    title: '已保存到相册',
                    icon: 'success'
                  });

                  // 显示金币扣除提示
                  setTimeout(() => {
                    wx.showToast({
                      title: `消耗1金币，剩余${remainingCoins}个`,
                      icon: 'none',
                      duration: 2000
                    });
                  }, 1500);
                },
                fail: (err) => {
                  console.error('保存视频失败:', err);
                  wx.hideLoading();

                  // 保存失败，引导用户手动下载
                  this.showManualDownloadGuide('视频', videoInfo.video_url);
                }
              });
            } else {
              wx.hideLoading();
              console.error('下载失败，状态码:', res.statusCode);

              // 下载失败，引导用户手动下载
              this.showManualDownloadGuide('视频', videoInfo.video_url);
            }
          },
          fail: (err) => {
            console.error('下载视频失败:', err);
            wx.hideLoading();

            // 下载失败，引导用户手动下载
            this.showManualDownloadGuide('视频', videoInfo.video_url);
          }
        });

        // 监听真实下载进度
        downloadTask.onProgressUpdate((res) => {
          const progress = Math.floor(res.progress);
          wx.showLoading({
            title: `下载进度 ${progress}%`,
          });
        });
      });
    } 
    // 保存图片
    else if (videoInfo.images && videoInfo.images.length > 0) {
      checkAndRequestPermission('image', () => {
        wx.showLoading({
          title: '正在保存图片...',
        });
        
        const totalImages = videoInfo.images.length;

        // 更新进度显示
        wx.showLoading({
          title: `保存图片 0/${totalImages}`,
        });

        // 递归保存多张图片
        const saveImages = (index) => {
          if (index >= totalImages) {
            // 扣除金币
            const remainingCoins = this.deductCoins(1);

            wx.hideLoading();
            wx.showToast({
              title: '已全部保存',
              icon: 'success'
            });

            // 显示金币扣除提示
            setTimeout(() => {
              wx.showToast({
                title: `消耗1金币，剩余${remainingCoins}个`,
                icon: 'none',
                duration: 2000
              });
            }, 1500);
            return;
          }

          // 更新进度
          wx.showLoading({
            title: `保存图片 ${index + 1}/${totalImages}`,
          });

          wx.downloadFile({
            url: videoInfo.images[index],
            success: (res) => {
              if (res.statusCode === 200) {
                wx.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    // 继续保存下一张
                    saveImages(index + 1);
                  },
                  fail: (err) => {
                    console.error('保存图片失败:', err);
                    wx.hideLoading();

                    // 保存失败，引导用户手动下载
                    this.showManualDownloadGuide('图片', videoInfo.images[index]);
                  }
                });
              } else {
                wx.hideLoading();
                console.error('下载图片失败，状态码:', res.statusCode);

                // 下载失败，引导用户手动下载
                this.showManualDownloadGuide('图片', videoInfo.images[index]);
              }
            },
            fail: (err) => {
              console.error('下载图片失败:', err);
              wx.hideLoading();

              // 下载失败，引导用户手动下载
              this.showManualDownloadGuide('图片', videoInfo.images[index]);
            }
          });
        };
        
        // 开始保存第一张
        saveImages(0);
      });
    }
  },
  
  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.videoInfo.images;
    
    wx.previewImage({
      current: images[index],
      urls: images
    });
  },
  
  // 分享功能
  shareVideo() {
    // 微信小程序分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  
  // 分享到聊天
  onShareAppMessage() {
    const videoInfo = this.data.videoInfo;
    return {
      title: videoInfo ? (videoInfo.title || '分享一个精彩视频') : '视频去水印',
      path: '/pages/index/index',
      imageUrl: videoInfo && videoInfo.cover_url ? videoInfo.cover_url : ''
    };
  }
})