// ========================================
// 抖音去水印小程序广告自动刷脚本 - 修复版
// 基于小程序源码分析优化
// 专门针对抖音去水印小程序的广告系统
// 作者：Auto.js助手
// 版本：v3.1 (修复版)
// ========================================

'ui';

// 等待Auto.js服务启动
try {
    auto.waitFor();
    console.log("Auto.js服务启动成功");
} catch (e) {
    console.log("Auto.js服务启动失败: " + e.message);
    toast("请确保Auto.js服务正常运行");
    exit();
}

// 检查Auto.js API是否可用
try {
    var apiTestMsg = "typeof text: " + typeof text + ", typeof desc: " + typeof desc + ", typeof id: " + typeof id;
    files.append("/sdcard/autojs_ad_log.txt", "[API TEST] " + apiTestMsg + "\n");

    // 检查关键API
    if (typeof text === 'undefined' || typeof desc === 'undefined' || typeof id === 'undefined') {
        throw new Error("关键API未定义");
    }
    console.log("API检查通过");
} catch (e) {
    console.log("API检查失败: " + e.message);
    toast("Auto.js API不可用，请检查版本兼容性");
    exit();
}

// 配置参数 - 优化版本避免无响应
var CONFIG = {
    totalTime: 10 * 60,        // 总运行时长10分钟（测试用）
    interval: 15,              // 每次操作间隔15秒
    adWaitTime: 30000,         // 广告等待时间30秒
    maxRetries: 3,             // 最大重试次数
    waitTimeout: 5000,         // 等待超时时间5秒
    huaweiOptimization: false, // 暂时关闭华为优化
    debugMode: true,           // 调试模式
    adButtonIcon: "📺",
    maxCoinsText: "金币已满"
};

// 基于小程序源码的界面元素识别配置
var UI_SELECTORS = {
    // 金币显示区域
    coinsDisplay: {
        text: ["💰", "金币"],
        desc: "金币显示"
    },

    // 广告按钮 - 基于小程序源码
    adButtons: [
        "观看广告获取金币",
        "观看广告",
        "看广告",
        "广告",
        "观看视频",
        "看视频",
        "视频广告",
        "激励视频",
        "获取奖励",
        "领取奖励",
        "获得金币",
        "赚取金币",
        "免费领取",
        "免费获得",
        "观看获得",
        "看广告获得",
        "广告奖励",
        "📺",  // 广告图标
        "金币已满"  // 当金币满时的按钮文本
    ],

    // 关闭广告按钮
    closeButtons: [
        "关闭广告",
        "跳过广告",
        "关闭",
        "跳过",
        "×",
        "X",
        "关闭视频",
        "跳过视频",
        "结束广告",
        "广告结束",
        "返回",
        "退出",
        "完成",
        "确定"
    ],

    // 广告完成后的确认按钮
    confirmButtons: [
        "确定",
        "确认",
        "知道了",
        "好的",
        "完成",
        "领取",
        "获得",
        "继续",
        "下一步",
        "太好了"  // 基于小程序源码
    ],

    // 金币奖励弹窗
    rewardModal: [
        "观看完成",
        "恭喜获得",
        "个金币",
        "太好了"
    ],

    // 错误提示
    errorMessages: [
        "网络错误",
        "加载失败",
        "广告加载失败",
        "请检查网络",
        "广告显示失败",
        "广告功能不可用",
        "请观看完整广告才能获得奖励"
    ]
};

// 全局变量
var startTime = new Date().getTime();
var adCount = 0;
var successCount = 0;
var errorCount = 0;
var lastAdTime = 0;
var currentCoins = 0;
var maxCoins = 5000; // 基于小程序源码

// 日志文件路径
var LOG_FILE = "/sdcard/autojs_ad_log.txt";

// 写入日志到文件
function writeLogFile(message) {
    try {
        files.append(LOG_FILE, message + "\n");
    } catch (e) {
        // 文件写入失败也不影响主流程
        console.log("日志写入失败: " + e.message);
    }
}

// 日志函数
function log(message) {
    var timestamp = new Date().toLocaleTimeString();
    var logMsg = "[" + timestamp + "] " + message;
    console.log(logMsg);
    toast(message);
    writeLogFile(logMsg);
}

// 华为手机检测和优化 (移除后台线程避免无响应)
function huaweiSetup() {
    try {
        var brand = device.brand.toLowerCase();
        var isHuawei = brand.includes("huawei");

        log("设备品牌: " + device.brand);
        log("设备型号: " + device.model);
        log("华为设备: " + (isHuawei ? "是" : "否"));

        if (isHuawei && CONFIG.huaweiOptimization) {
            // 华为手机优化设置
            setScreenMetrics(device.width, device.height);
            log("华为优化已启用");

            // 移除后台线程，避免导致Auto.js无响应
            log("华为优化配置完成（已移除后台线程）");
        }

        return isHuawei;
    } catch (e) {
        log("华为设置失败: " + e.message);
        return false;
    }
}

// 权限检查
function checkPermissions() {
    log("=== 权限检查 ===");

    // 检查无障碍服务
    try {
        if (!auto.service) {
            log("请开启无障碍服务");
            auto.waitFor();
        }
    } catch (e) {
        log("无障碍服务检查失败: " + e.message);
        toast("请手动开启无障碍服务");
        exit();
    }

    // 检查屏幕录制权限
    try {
        if (!requestScreenCapture()) {
            log("需要屏幕录制权限");
            toast("请授予屏幕录制权限");
            exit();
        }
    } catch (e) {
        log("屏幕录制权限检查失败: " + e.message);
        toast("屏幕录制权限获取失败");
        exit();
    }

    log("权限检查通过");
}

// 智能查找元素 - 基于小程序源码优化
function findElement(selectors, timeout, description) {
    timeout = timeout || CONFIG.waitTimeout;

    try {
        for (var i = 0; i < selectors.length; i++) {
            var selector = selectors[i];
            var element = null;

            if (typeof selector === 'string') {
                // 文本查找
                element = text(selector).findOne(timeout);
            } else if (selector.type === 'desc') {
                // 描述查找
                element = desc(selector.value).findOne(timeout);
            } else if (selector.type === 'id') {
                // ID查找
                element = id(selector.value).findOne(timeout);
            } else if (selector.type === 'className') {
                // 类名查找
                element = className(selector.value).findOne(timeout);
            }

            if (element) {
                log("找到元素: " + selector + " (" + description + ")");
                return element;
            }
        }
    } catch (e) {
        log("查找元素出错: " + description + " - " + e.message);
    }

    return null;
}

// 智能点击（华为优化）
function smartClick(element, description) {
    if (!element) {
        log("元素不存在，无法点击: " + description);
        return false;
    }

    try {
        var bounds = element.bounds();
        var x = bounds.centerX();
        var y = bounds.centerY();

        // 华为触摸优化：添加随机偏移
        x += random(-8, 8);
        y += random(-8, 8);

        // 华为点击优化：模拟真实触摸
        press(x, y, 50); // 50ms按压时间

        log("点击成功: " + description);
        return true;
    } catch (e) {
        log("点击失败: " + description + " - " + e.message);
        return false;
    }
}

// 检查微信小程序状态
function checkWechatMiniProgram() {
    try {
        // 检查是否在微信中
        var currentPkg = currentPackage();
        if (currentPkg !== "com.tencent.mm") {
            log("当前不在微信中，请先打开微信小程序");
            return false;
        }

        // 检查是否在抖音去水印小程序中
        var miniProgramIndicator = findElement([
            "抖音去水印下载",
            "抖音视频无水印下载",
            "快速提取原始高清视频",
            "一键解析",
            "观看广告获取金币"
        ], 2000, "小程序标识");

        if (!miniProgramIndicator) {
            log("未检测到抖音去水印小程序界面");
            return false;
        }

        return true;
    } catch (e) {
        log("检查微信小程序状态失败: " + e.message);
        return false;
    }
}

// 检查金币状态 - 基于小程序源码
function checkCoinsStatus() {
    log("检查金币状态...");

    try {
        // 查找金币显示
        var coinsElement = findElement([
            "💰",
            "金币",
            "0/5000",
            "1/5000",
            "2/5000"
        ], 3000, "金币显示");

        if (coinsElement) {
            var coinsText = coinsElement.text();
            log("当前金币状态: " + coinsText);

            // 解析金币数量
            var match = coinsText.match(/(\d+)\/(\d+)/);
            if (match) {
                currentCoins = parseInt(match[1]);
                maxCoins = parseInt(match[2]);
                log("当前金币: " + currentCoins + "/" + maxCoins);

                if (currentCoins >= maxCoins) {
                    log("金币已满，无需观看广告");
                    return false;
                }
            }
        }

        return true;
    } catch (e) {
        log("检查金币状态失败: " + e.message);
        return true; // 出错时继续尝试
    }
}

// 等待广告结束 - 基于小程序源码优化
function waitForAdEnd() {
    log("等待广告结束...");

    var startTime = new Date().getTime();
    var timeout = CONFIG.adWaitTime;

    while ((new Date().getTime() - startTime) < timeout) {
        try {
            // 查找关闭按钮
            var closeBtn = findElement(UI_SELECTORS.closeButtons, 2000, "关闭广告按钮");
            if (closeBtn) {
                if (smartClick(closeBtn, "关闭广告")) {
                    sleep(1000);

                    // 查找确认按钮
                    var confirmBtn = findElement(UI_SELECTORS.confirmButtons, 3000, "确认按钮");
                    if (confirmBtn) {
                        smartClick(confirmBtn, "确认按钮");
                        sleep(1000);
                    }

                    return true;
                }
            }

            // 检查是否有错误提示
            var errorText = findElement(UI_SELECTORS.errorMessages, 1000, "错误提示");
            if (errorText) {
                log("广告播放出错: " + errorText.text());
                back();
                sleep(2000);
                return false;
            }

            // 检查金币奖励弹窗
            var rewardText = findElement(UI_SELECTORS.rewardModal, 1000, "奖励弹窗");
            if (rewardText) {
                log("检测到奖励弹窗");
                // 点击确认按钮
                var confirmBtn = findElement(UI_SELECTORS.confirmButtons, 3000, "确认按钮");
                if (confirmBtn) {
                    smartClick(confirmBtn, "确认奖励");
                    sleep(1000);
                    return true;
                }
            }

            sleep(1000);
        } catch (e) {
            log("等待广告结束出错: " + e.message);
        }
    }

    log("广告等待超时，尝试返回");
    back();
    sleep(2000);
    return false;
}

// 查找并点击广告按钮 - 基于小程序源码
function findAndClickAd() {
    log("查找广告按钮...");

    try {
        // 先检查金币状态
        if (!checkCoinsStatus()) {
            return false;
        }

        // 查找广告按钮
        var adBtn = findElement(UI_SELECTORS.adButtons, CONFIG.waitTimeout, "广告按钮");

        if (adBtn) {
            // 检查按钮是否可用
            var btnText = adBtn.text();
            if (btnText.includes("金币已满") || btnText.includes("已满")) {
                log("金币已满，按钮不可用");
                return false;
            }

            // 检查是否在合理的时间间隔内
            var currentTime = new Date().getTime();
            if (currentTime - lastAdTime < CONFIG.interval * 1000) {
                log("时间间隔太短，跳过");
                return false;
            }

            // 点击广告按钮
            if (smartClick(adBtn, "广告按钮")) {
                lastAdTime = currentTime;
                adCount++;
                log("第" + adCount + "次广告开始");

                // 等待广告结束
                if (waitForAdEnd()) {
                    successCount++;
                    log("第" + adCount + "次广告完成");

                    // 更新金币状态
                    sleep(2000);
                    checkCoinsStatus();

                    return true;
                } else {
                    log("第" + adCount + "次广告失败");
                    return false;
                }
            }
        } else {
            log("未找到广告按钮");
            return false;
        }

        return false;
    } catch (e) {
        log("查找并点击广告出错: " + e.message);
        return false;
    }
}

// 主循环 - 添加安全检查避免无响应
function mainLoop() {
    log("=== 开始抖音去水印小程序广告自动刷 ===");
    log("总时长: " + Math.floor(CONFIG.totalTime / 60) + "分钟");
    log("操作间隔: " + CONFIG.interval + "秒");
    log("最大金币: " + maxCoins);

    var loopCount = 0;
    var maxLoops = Math.floor(CONFIG.totalTime / CONFIG.interval); // 计算最大循环次数

    while (loopCount < maxLoops && (new Date().getTime() - startTime) / 1000 < CONFIG.totalTime) {
        try {
            loopCount++;
            log("执行第 " + loopCount + "/" + maxLoops + " 次循环");

            // 检查微信小程序状态
            if (!checkWechatMiniProgram()) {
                log("等待抖音去水印小程序...");
                sleep(5000);
                continue;
            }

            // 查找并点击广告
            if (findAndClickAd()) {
                log("广告处理成功");
            } else {
                log("广告处理失败");
                errorCount++;
            }

            // 检查是否需要停止
            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止脚本");
                break;
            }

            // 等待间隔
            log("等待" + CONFIG.interval + "秒后继续...");
            sleep(CONFIG.interval * 1000);

        } catch (e) {
            errorCount++;
            log("执行出错: " + e.message);

            if (errorCount >= CONFIG.maxRetries) {
                log("错误次数过多，停止脚本");
                break;
            }

            sleep(3000);
        }
    }

    log("主循环结束，总循环次数: " + loopCount);
}

// 显示统计信息
function showStatistics() {
    var endTime = new Date().getTime();
    var totalTime = Math.floor((endTime - startTime) / 1000);
    var minutes = Math.floor(totalTime / 60);
    var seconds = totalTime % 60;
    var successRate = adCount > 0 ? Math.round((successCount / adCount) * 100) : 0;
    var avgInterval = successCount > 0 ? Math.round(totalTime / successCount) : 0;

    log("=== 抖音去水印小程序广告刷取完成 ===");
    log("总运行时间: " + minutes + "分" + seconds + "秒");
    log("总广告次数: " + adCount + "次");
    log("成功次数: " + successCount + "次");
    log("失败次数: " + (adCount - successCount) + "次");
    log("成功率: " + successRate + "%");
    log("平均间隔: " + avgInterval + "秒/次");
    log("最终金币: " + currentCoins + "/" + maxCoins);
}

// 主程序入口
function main() {
    log("=== 抖音去水印小程序广告自动刷脚本启动 ===");

    try {
        // 华为手机设置
        var isHuawei = huaweiSetup();

        // 检查权限
        checkPermissions();

        // 设置屏幕分辨率
        setScreenMetrics(device.width, device.height);
        log("屏幕分辨率: " + device.width + "x" + device.height);

        // 显示使用说明
        log("=== 使用说明 ===");
        log("1. 请先打开微信中的抖音去水印小程序");
        log("2. 确保小程序界面显示正常");
        log("3. 脚本将自动识别并点击广告按钮");
        log("4. 按音量下键可停止脚本");
        log("5. 基于小程序源码优化，支持金币系统");

        // 延迟启动
        sleep(3000);

        // 开始主循环
        mainLoop();

        // 显示统计信息
        showStatistics();

    } catch (e) {
        var errMsg = "[ERROR] " + e.message + "\n" + e.stack;
        log(errMsg);
        writeLogFile(errMsg);
        toast("脚本异常终止，请查看日志文件");
    }
}

// 启动脚本
main();
